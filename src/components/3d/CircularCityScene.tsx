
import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, PerspectiveCamera } from '@react-three/drei';
import { Mesh, Group } from 'three';
import '../3d/ThreeHelpers';

const CircularCity = () => {
  const group = useRef<Group>(null);
  
  useFrame(() => {
    if (group.current) {
      group.current.rotation.y += 0.001;
    }
  });

  // Create a simple circular city model
  return (
    <group ref={group}>
      {/* Central hub */}
      <mesh position={[0, 0, 0]}>
        <cylinderGeometry args={[5, 5, 2, 32]} />
        <meshStandardMaterial color="#008751" metalness={0.5} roughness={0.2} />
      </mesh>
      
      {/* Outer ring */}
      <mesh position={[0, -0.5, 0]}>
        <torusGeometry args={[15, 2, 16, 100]} />
        <meshStandardMaterial color="#A0522D" metalness={0.3} roughness={0.6} />
      </mesh>
      
      {/* Radial sections */}
      {Array(8).fill(0).map((_, i) => {
        const angle = (i / 8) * Math.PI * 2;
        const x = Math.cos(angle) * 10;
        const z = Math.sin(angle) * 10;
        
        return (
          <mesh key={i} position={[x, 0, z]}>
            <boxGeometry args={[3, 4, 3]} />
            <meshStandardMaterial color="#DAA520" metalness={0.4} roughness={0.3} />
          </mesh>
        );
      })}
      
      {/* Connecting roads */}
      {Array(8).fill(0).map((_, i) => {
        const angle = (i / 8) * Math.PI * 2;
        const x1 = Math.cos(angle) * 5;
        const z1 = Math.sin(angle) * 5;
        const x2 = Math.cos(angle) * 9;
        const z2 = Math.sin(angle) * 9;
        
        const midX = (x1 + x2) / 2;
        const midZ = (z1 + z2) / 2;
        const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(z2 - z1, 2));
        
        return (
          <mesh 
            key={i} 
            position={[midX, -0.9, midZ]}
            rotation={[0, Math.atan2(z2 - z1, x2 - x1), 0]}
          >
            <boxGeometry args={[length, 0.1, 0.5]} />
            <meshStandardMaterial color="#D2B48C" metalness={0.1} roughness={0.7} />
          </mesh>
        );
      })}
    </group>
  );
};

const CircularCityScene: React.FC = () => {
  return (
    <div className="canvas-container" style={{ height: '400px', width: '100%' }}>
      <Canvas shadows>
        <PerspectiveCamera makeDefault position={[0, 15, 25]} />
        <ambientLight intensity={0.5} />
        <directionalLight 
          position={[10, 10, 10]} 
          intensity={1} 
          castShadow 
          shadow-mapSize-width={1024} 
          shadow-mapSize-height={1024}
        />
        <CircularCity />
        <Environment preset="sunset" />
        <OrbitControls 
          enableZoom={false}
          enablePan={false}
          maxPolarAngle={Math.PI / 2.5}
          minPolarAngle={Math.PI / 4}
        />
      </Canvas>
    </div>
  );
};

export default CircularCityScene;
