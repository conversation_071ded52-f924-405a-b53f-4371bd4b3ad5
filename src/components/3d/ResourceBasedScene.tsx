
import React from 'react';
import { motion } from 'framer-motion';

const ResourceBasedScene: React.FC = () => {
  // Define nodes with the new uploaded images and Kenya-focused content
  const resourceNodes = [
    { 
      title: "Kenya's Geological Wealth", 
      description: "A comprehensive map of Kenya's diverse geological formations that form the foundation of our resources", 
      imageUrl: "/lovable-uploads/4f84d19c-5131-4f0a-a4ee-772427c1855c.png" 
    },
    { 
      title: "Mineral Resources Distribution", 
      description: "Key mineral deposits across Kenya including gold, iron ore, titanium, and other critical resources", 
      imageUrl: "/lovable-uploads/daf004be-56b7-4ab7-af44-0d72ee5e283e.png" 
    },
    { 
      title: "Major Urban Centers", 
      description: "Strategic locations for development including Mombasa, Kenya's second-largest city with over one million residents", 
      imageUrl: "/lovable-uploads/a518ba33-4cdf-444d-a2f0-a936ea3ded3e.png" 
    },
    { 
      title: "Circular City Design", 
      description: "Efficient urban planning models for Kenya's future cities featuring sustainable infrastructure", 
      imageUrl: "/lovable-uploads/d3ff8ac2-22c2-49e3-9939-fb0ee5b852d1.png" 
    },
    { 
      title: "Interconnected Communities", 
      description: "A network of circular cities designed to maximize resource efficiency across Kenya's regions", 
      imageUrl: "/lovable-uploads/78f355fa-21c5-45bc-91ff-cc964589e50c.png" 
    },
    { 
      title: "Sustainable Architecture", 
      description: "Modern, efficient building designs adapted for Kenya's climate and cultural context", 
      imageUrl: "/lovable-uploads/48e831ea-ff54-4b5b-a338-60832c3362a1.png" 
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-8">
      {resourceNodes.map((node, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.5 }}
          viewport={{ once: true }}
          className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
        >
          <div className="h-60 overflow-hidden">
            <img 
              src={node.imageUrl} 
              alt={node.title} 
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" 
            />
          </div>
          <div className="p-6">
            <h3 className="text-xl font-bold mb-2 text-green-600">{node.title}</h3>
            <p className="text-gray-700">{node.description}</p>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default ResourceBasedScene;
