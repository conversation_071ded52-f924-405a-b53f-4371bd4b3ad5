
import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Group, Shape } from 'three';
import '../3d/ThreeHelpers';

const KenyaMap = () => {
  const group = useRef<Group>(null);
  
  useFrame(() => {
    if (group.current) {
      group.current.rotation.y += 0.002;
    }
  });

  // Simplified Kenya map outline - stylized representation
  const createPoint = (x: number, y: number, z: number): [number, number, number] => [x, y, z];
  
  // These are simplified points representing Kenya's outline
  const kenyaOutlinePoints = [
    createPoint(-3, 0, 4), // Northwestern point
    createPoint(-1, 0, 5), // Northern point
    createPoint(2, 0, 4),  // Northeastern point
    createPoint(3, 0, 0),  // Eastern coast
    createPoint(1, 0, -4), // Southeastern point
    createPoint(-2, 0, -3), // Southwestern point
    createPoint(-3, 0, 0),  // Western point
    createPoint(-3, 0, 4)   // Back to start
  ];

  const shape = new Shape();
  shape.moveTo(kenyaOutlinePoints[0][0], kenyaOutlinePoints[0][2]);
  for (let i = 1; i < kenyaOutlinePoints.length; i++) {
    shape.lineTo(kenyaOutlinePoints[i][0], kenyaOutlinePoints[i][2]);
  }

  return (
    <group ref={group}>
      {/* Base of Kenya */}
      <mesh position={[0, -0.1, 0]} receiveShadow>
        <extrudeGeometry 
          args={[
            shape,
            { depth: 0.2, bevelEnabled: true, bevelThickness: 0.1, bevelSize: 0.1 }
          ]}
        />
        <meshStandardMaterial color="#008751" metalness={0.3} roughness={0.7} />
      </mesh>
      
      {/* Mount Kenya */}
      <mesh position={[0, 0, 0]} castShadow>
        <coneGeometry args={[1, 2, 32]} />
        <meshStandardMaterial color="#A0522D" metalness={0.2} roughness={0.8} />
      </mesh>
      
      {/* Cities as small spheres */}
      <mesh position={[0, 0.1, -2]} castShadow> {/* Nairobi */}
        <sphereGeometry args={[0.3, 16, 16]} />
        <meshStandardMaterial color="#E94B3C" metalness={0.5} roughness={0.5} />
      </mesh>
      
      <mesh position={[2, 0.1, 0]} castShadow> {/* Mombasa */}
        <sphereGeometry args={[0.25, 16, 16]} />
        <meshStandardMaterial color="#F2C12E" metalness={0.5} roughness={0.5} />
      </mesh>
      
      <mesh position={[-2, 0.1, 2]} castShadow> {/* Kisumu */}
        <sphereGeometry args={[0.2, 16, 16]} />
        <meshStandardMaterial color="#4682B4" metalness={0.5} roughness={0.5} />
      </mesh>
    </group>
  );
};

const KenyaMapScene: React.FC = () => {
  return (
    <div className="canvas-container" style={{ height: '400px', width: '100%' }}>
      <Canvas shadows>
        <ambientLight intensity={0.5} />
        <directionalLight 
          position={[10, 10, 5]} 
          intensity={1} 
          castShadow 
        />
        <KenyaMap />
        <Environment preset="sunset" />
        <OrbitControls 
          enableZoom={false}
          enablePan={false}
          autoRotate
          autoRotateSpeed={0.5}
        />
      </Canvas>
    </div>
  );
};

export default KenyaMapScene;
