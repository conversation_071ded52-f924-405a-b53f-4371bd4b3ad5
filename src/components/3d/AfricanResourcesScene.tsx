
import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, Text } from '@react-three/drei';
import { Group } from 'three';
import '../3d/ThreeHelpers';

const ResourceSphere = ({ position, color, size, label, labelOffset = [0, 1.3, 0] as [number, number, number] }: 
  { position: [number, number, number], color: string, size: number, label: string, labelOffset?: [number, number, number] }) => {
  
  const ref = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.y += 0.01;
      ref.current.position.y = position[1] + Math.sin(state.clock.getElapsedTime()) * 0.1;
    }
  });
  
  return (
    <group position={position}>
      <mesh ref={ref} castShadow>
        <sphereGeometry args={[size, 32, 32]} />
        <meshStandardMaterial color={color} metalness={0.4} roughness={0.6} />
      </mesh>
      <Text
        position={labelOffset}
        fontSize={0.5}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {label}
      </Text>
    </group>
  );
};

const AfricanResources = () => {
  const group = useRef<Group>(null);
  
  // Resources arranged in a circle
  const resources = [
    { position: [0, 0.5, -4] as [number, number, number], color: "#A0522D", size: 0.7, label: "Minerals" },
    { position: [3, 0.5, -2] as [number, number, number], color: "#DAA520", size: 0.8, label: "Gold" },
    { position: [4, 0.5, 1] as [number, number, number], color: "#008751", size: 0.9, label: "Agriculture" },
    { position: [2, 0.5, 3] as [number, number, number], color: "#4682B4", size: 0.75, label: "Water" },
    { position: [-1, 0.5, 4] as [number, number, number], color: "#E94B3C", size: 0.7, label: "Culture" },
    { position: [-3, 0.5, 2] as [number, number, number], color: "#231F20", size: 0.8, label: "Oil" },
    { position: [-4, 0.5, -1] as [number, number, number], color: "#FF7F50", size: 0.7, label: "Wildlife" }
  ];
  
  return (
    <group ref={group}>
      {/* African continent base */}
      <mesh position={[0, 0, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[10, 10]} />
        <meshStandardMaterial color="#D2B48C" metalness={0.1} roughness={0.9} />
      </mesh>
      
      {/* Resource spheres */}
      {resources.map((resource, index) => (
        <ResourceSphere
          key={index}
          position={resource.position}
          color={resource.color}
          size={resource.size}
          label={resource.label}
        />
      ))}
    </group>
  );
};

const AfricanResourcesScene: React.FC = () => {
  return (
    <div className="canvas-container" style={{ height: '400px', width: '100%' }}>
      <Canvas shadows>
        <ambientLight intensity={0.5} />
        <directionalLight 
          position={[10, 10, 5]} 
          intensity={1} 
          castShadow 
        />
        <AfricanResources />
        <Environment preset="sunset" />
        <OrbitControls 
          enableZoom={false}
          enablePan={false}
          maxPolarAngle={Math.PI / 2.5}
        />
      </Canvas>
    </div>
  );
};

export default AfricanResourcesScene;
