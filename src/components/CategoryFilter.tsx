import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'

interface Category {
  id: string
  name: string
  slug: string
  color: string
  count?: number
}

interface CategoryFilterProps {
  categories: Category[]
  selectedCategory: string | null
  onCategorySelect: (categoryId: string | null) => void
  className?: string
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="flex flex-wrap gap-3 items-center">
        {/* All Categories Button */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0 }}
        >
          <Button
            variant={selectedCategory === null ? "default" : "outline"}
            size="sm"
            onClick={() => onCategorySelect(null)}
            className={`
              transition-all duration-300 hover:scale-105
              ${selectedCategory === null 
                ? 'bg-africa-green text-white shadow-lg' 
                : 'border-africa-green text-africa-green hover:bg-africa-green hover:text-white'
              }
            `}
          >
            All Categories
            {selectedCategory === null && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="ml-2"
              >
                ✨
              </motion.div>
            )}
          </Button>
        </motion.div>

        {/* Category Buttons */}
        {categories.map((category, index) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: (index + 1) * 0.1 }}
          >
            <Button
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => onCategorySelect(category.id)}
              className={`
                relative transition-all duration-300 hover:scale-105 group
                ${selectedCategory === category.id
                  ? 'text-white shadow-lg border-0' 
                  : 'border-2 hover:text-white'
                }
              `}
              style={{
                backgroundColor: selectedCategory === category.id ? category.color : 'transparent',
                borderColor: category.color,
                color: selectedCategory === category.id ? 'white' : category.color
              }}
              onMouseEnter={(e) => {
                if (selectedCategory !== category.id) {
                  e.currentTarget.style.backgroundColor = category.color
                  e.currentTarget.style.color = 'white'
                }
              }}
              onMouseLeave={(e) => {
                if (selectedCategory !== category.id) {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.color = category.color
                }
              }}
            >
              <span className="flex items-center">
                {category.name}
                {category.count !== undefined && (
                  <Badge 
                    variant="secondary" 
                    className="ml-2 text-xs bg-white/20 text-current border-0"
                  >
                    {category.count}
                  </Badge>
                )}
              </span>
              
              {selectedCategory === category.id && (
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  className="ml-2"
                >
                  <X size={14} />
                </motion.div>
              )}
            </Button>
          </motion.div>
        ))}
      </div>

      {/* Active Filter Indicator */}
      {selectedCategory && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mt-4 flex items-center space-x-2"
        >
          <span className="text-sm text-gray-600">Filtering by:</span>
          <Badge 
            className="text-white border-0"
            style={{ 
              backgroundColor: categories.find(c => c.id === selectedCategory)?.color || '#008751'
            }}
          >
            {categories.find(c => c.id === selectedCategory)?.name}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCategorySelect(null)}
            className="text-gray-500 hover:text-gray-700 p-1 h-auto"
          >
            <X size={16} />
          </Button>
        </motion.div>
      )}
    </div>
  )
}

export default CategoryFilter
