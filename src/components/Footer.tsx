
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Facebook, Twitter, Youtube, Instagram, Mail } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-africa-black text-white pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <div className="h-8 w-8 rounded-full african-gradient"></div>
              <span className="font-montserrat font-semibold text-lg text-white">Kennect-Afrinnect</span>
            </div>
            <p className="text-gray-300 mb-6">
              Building an African Resource-Based Economy for a world beyond scarcity, poverty, and environmental degradation.
            </p>
            <div className="flex space-x-4">
              <a href="#" aria-label="Facebook" className="text-gray-300 hover:text-africa-gold transition">
                <Facebook size={20} />
              </a>
              <a href="#" aria-label="Twitter" className="text-gray-300 hover:text-africa-gold transition">
                <Twitter size={20} />
              </a>
              <a href="#" aria-label="Youtube" className="text-gray-300 hover:text-africa-gold transition">
                <Youtube size={20} />
              </a>
              <a href="#" aria-label="Instagram" className="text-gray-300 hover:text-africa-gold transition">
                <Instagram size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-montserrat font-medium text-lg mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li><Link to="/" className="text-gray-300 hover:text-africa-gold transition">Home</Link></li>
              <li><Link to="/about" className="text-gray-300 hover:text-africa-gold transition">About</Link></li>
              <li><Link to="/resources" className="text-gray-300 hover:text-africa-gold transition">Resources</Link></li>
              <li><Link to="/whitepaper" className="text-gray-300 hover:text-africa-gold transition">Whitepaper</Link></li>
              <li><Link to="/blog" className="text-gray-300 hover:text-africa-gold transition">Blog</Link></li>
              <li><Link to="/gallery" className="text-gray-300 hover:text-africa-gold transition">Gallery</Link></li>
              <li><Link to="/visionary" className="text-gray-300 hover:text-africa-gold transition">Visionary</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-africa-gold transition">Contact</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="font-montserrat font-medium text-lg mb-6">Resources</h3>
            <ul className="space-y-3">
              <li><a href="#" className="text-gray-300 hover:text-africa-gold transition">FAQs</a></li>
              <li><a href="#" className="text-gray-300 hover:text-africa-gold transition">Research</a></li>
              <li><a href="#" className="text-gray-300 hover:text-africa-gold transition">Projects</a></li>
              <li><a href="#" className="text-gray-300 hover:text-africa-gold transition">Education</a></li>
              <li><a href="#" className="text-gray-300 hover:text-africa-gold transition">Privacy Policy</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-montserrat font-medium text-lg mb-6">Contact Us</h3>
            <div className="flex items-center space-x-3 mb-4">
              <Mail size={18} />
              <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-africa-gold transition"><EMAIL></a>
            </div>
            <p className="text-gray-300">
              Join our newsletter to stay updated on our progress and events.
            </p>
            <div className="mt-4">
              <Link to="/contact">
                <button className="bg-africa-green hover:bg-opacity-80 text-white py-2 px-4 rounded transition">
                  Subscribe
                </button>
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} Kennect-Afrinnect. All rights reserved.</p>
          <p className="mt-2 text-sm">
            Inspired by <a href="https://www.thevenusproject.com/" target="_blank" rel="noopener noreferrer" className="underline hover:text-white transition">The Venus Project</a>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
