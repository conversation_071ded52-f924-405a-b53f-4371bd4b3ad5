import React, { useState } from 'react'
import ReactPlayer from 'react-player'
import { motion } from 'framer-motion'
import { Play, Pause, Volume2, VolumeX, Maximize, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface VideoPlayerProps {
  url: string
  platform?: 'youtube' | 'tiktok' | 'instagram' | 'vimeo'
  title?: string
  thumbnail?: string
  autoplay?: boolean
  className?: string
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  url,
  platform,
  title,
  thumbnail,
  autoplay = false,
  className = ''
}) => {
  const [playing, setPlaying] = useState(autoplay)
  const [muted, setMuted] = useState(false)
  const [showControls, setShowControls] = useState(true)

  const getPlatformInfo = () => {
    switch (platform) {
      case 'youtube':
        return { name: 'YouTube', icon: '🎥', color: '#FF0000' }
      case 'tiktok':
        return { name: 'TikTok', icon: '🎵', color: '#000000' }
      case 'instagram':
        return { name: 'Instagram', icon: '📸', color: '#E4405F' }
      case 'vimeo':
        return { name: 'Vimeo', icon: '🎬', color: '#1AB7EA' }
      default:
        return { name: 'Video', icon: '▶️', color: '#008751' }
    }
  }

  const platformInfo = getPlatformInfo()

  const handlePlayPause = () => {
    setPlaying(!playing)
  }

  const handleMute = () => {
    setMuted(!muted)
  }

  const openInNewTab = () => {
    window.open(url, '_blank')
  }

  return (
    <div className={`relative w-full ${className}`}>
      {/* Platform Badge */}
      {platform && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-4 left-4 z-10"
        >
          <Badge
            className="text-white border-0 shadow-lg backdrop-blur-sm"
            style={{ backgroundColor: platformInfo.color }}
          >
            {platformInfo.icon} {platformInfo.name}
          </Badge>
        </motion.div>
      )}

      {/* Video Container */}
      <div
        className="relative aspect-video bg-black rounded-lg overflow-hidden shadow-xl group"
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => setShowControls(false)}
      >
        <ReactPlayer
          url={url}
          width="100%"
          height="100%"
          playing={playing}
          muted={muted}
          controls={true}
          light={thumbnail}
          pip={true}
          className="react-player"
          config={{
            youtube: {
              playerVars: {
                showinfo: 1,
                modestbranding: 1,
                rel: 0,
                autoplay: 0,
                controls: 1,
                fs: 1,
                playsinline: 1
              }
            },
            vimeo: {
              playerOptions: {
                byline: false,
                portrait: false,
                title: false,
                autoplay: false,
                controls: true
              }
            }
          }}
        />

        {/* Custom Controls Overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: showControls ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30 flex items-center justify-center"
        >
          {/* Play/Pause Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePlayPause}
            className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-0 w-16 h-16 rounded-full"
          >
            {playing ? <Pause size={32} /> : <Play size={32} />}
          </Button>

          {/* Bottom Controls */}
          <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {/* Mute Button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleMute}
                className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-0 w-10 h-10 rounded-full"
              >
                {muted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </Button>

              {/* Title */}
              {title && (
                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-1">
                  <p className="text-white text-sm font-medium truncate max-w-xs">
                    {title}
                  </p>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {/* External Link Button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={openInNewTab}
                className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-0 w-10 h-10 rounded-full"
              >
                <ExternalLink size={20} />
              </Button>

              {/* Fullscreen Button */}
              <Button
                variant="ghost"
                size="icon"
                className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-0 w-10 h-10 rounded-full"
              >
                <Maximize size={20} />
              </Button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Video Info */}
      {title && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mt-4 p-4 bg-white/80 backdrop-blur-sm rounded-lg"
        >
          <h3 className="text-lg font-semibold text-africa-black mb-2">{title}</h3>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Watch on {platformInfo.name}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={openInNewTab}
              className="border-africa-green text-africa-green hover:bg-africa-green hover:text-white"
            >
              <ExternalLink size={16} className="mr-2" />
              Open Original
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default VideoPlayer
