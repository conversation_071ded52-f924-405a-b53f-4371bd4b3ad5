
import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Heart } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const donationSchema = z.object({
  amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, {
    message: "Amount must be greater than zero",
  }),
  name: z.string().min(2, {
    message: "Name must be at least 2 characters",
  }),
  email: z.string().email({
    message: "Please enter a valid email address",
  }),
});

type DonationFormValues = z.infer<typeof donationSchema>;

interface DonationFormProps {
  recipientName?: string;
  minimumAmount?: number;
  onDonationSubmit?: (values: DonationFormValues) => void;
}

const DonationForm: React.FC<DonationFormProps> = ({
  recipientName = "Kennect-Afrinnect",
  minimumAmount = 5,
  onDonationSubmit,
}) => {
  const { toast } = useToast();
  
  const form = useForm<DonationFormValues>({
    resolver: zodResolver(donationSchema),
    defaultValues: {
      amount: minimumAmount.toString(),
      name: "",
      email: "",
    },
  });

  const handleSubmit = (values: DonationFormValues) => {
    toast({
      title: "Donation initialized",
      description: `Processing your donation of ${values.amount} to ${recipientName}`,
    });
    
    if (onDonationSubmit) {
      onDonationSubmit(values);
    }
    // In a real app, this would redirect to a payment processor
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="text-center mb-6">
        <div className="mx-auto h-12 w-12 rounded-full bg-africa-green/20 flex items-center justify-center">
          <Heart className="h-6 w-6 text-africa-green" />
        </div>
        <h3 className="font-bold text-lg mt-3">Support {recipientName}</h3>
        <p className="text-sm text-gray-600">
          Your contribution helps build a sustainable Resource-Based Economy in Africa
        </p>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Donation Amount</FormLabel>
                <FormControl>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                    <Input 
                      type="number"
                      min={minimumAmount}
                      step="any"
                      className="pl-7"
                      placeholder="0.00"
                      {...field} 
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Your Name</FormLabel>
                <FormControl>
                  <Input placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="pt-2">
            <Button type="submit" className="w-full bg-africa-green hover:bg-africa-earth">
              Donate Now
            </Button>
          </div>
        </form>
      </Form>
      
      <div className="mt-4 text-xs text-center text-gray-500">
        <p>We accept PayPal, M-Pesa, and Crypto payments</p>
      </div>
    </div>
  );
};

export default DonationForm;
