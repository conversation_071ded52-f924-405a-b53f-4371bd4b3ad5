
import React from "react";
import { Users } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

interface GroupCardProps {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  topicCount?: number;
  coverImage?: string;
  category: string;
  isJoined?: boolean;
  onJoinGroup?: () => void;
}

const GroupCard: React.FC<GroupCardProps> = ({
  id,
  name,
  description,
  memberCount,
  topicCount = 0,
  coverImage,
  category,
  isJoined = false,
  onJoinGroup,
}) => {
  return (
    <Card className="overflow-hidden h-full flex flex-col transition-all duration-200 hover:shadow-md">
      <div className="h-32 relative overflow-hidden">
        {coverImage ? (
          <img 
            src={coverImage} 
            alt={name} 
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-africa-green/20 flex items-center justify-center">
            <Users className="h-12 w-12 text-africa-green/40" />
          </div>
        )}
        <div className="absolute top-3 right-3">
          <span className="bg-black/60 text-white text-xs px-2 py-1 rounded-full">
            {category}
          </span>
        </div>
      </div>
      
      <CardHeader className="pb-2">
        <Link to={`/groups/${id}`}>
          <h3 className="font-bold text-lg hover:text-africa-green transition-colors">
            {name}
          </h3>
        </Link>
      </CardHeader>
      
      <CardContent className="pb-4 flex-grow">
        <p className="text-sm text-gray-600 line-clamp-3">
          {description}
        </p>
      </CardContent>
      
      <CardFooter className="border-t p-4 pt-3 bg-gray-50 flex items-center justify-between">
        <div className="text-xs text-gray-600">
          <span>{memberCount} members</span>
          {topicCount > 0 && (
            <span className="ml-3">{topicCount} topics</span>
          )}
        </div>
        
        <Button
          variant={isJoined ? "outline" : "default"}
          size="sm"
          className={isJoined ? "border-africa-green text-africa-green hover:bg-africa-green/10" : "bg-africa-green hover:bg-africa-earth"}
          onClick={onJoinGroup}
        >
          {isJoined ? "Joined" : "Join Group"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default GroupCard;
