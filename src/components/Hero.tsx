
import React from "react";
import { Link } from "react-router-dom";
import { ArrowRight, Download } from "lucide-react";
import { Button } from "@/components/ui/button";

interface HeroProps {
  title: string;
  subtitle: string;
  showButtons?: boolean;
  backgroundImage?: string;
  alignment?: 'center' | 'left';
}

const Hero: React.FC<HeroProps> = ({ 
  title, 
  subtitle, 
  showButtons = true,
  backgroundImage = "https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80",
  alignment = 'center'
}) => {
  return (
    <div className="relative h-[70vh] min-h-[500px] flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat z-0" 
        style={{ backgroundImage: `url('${backgroundImage}')` }}
      ></div>
      
      {/* Overlay */}
      <div className="absolute inset-0 hero-overlay z-10"></div>
      
      {/* Content */}
      <div className={`container mx-auto px-4 z-20 ${alignment === 'center' ? 'text-center' : 'text-left'}`}>
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
          {title}
        </h1>
        <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
          {subtitle}
        </p>
        
        {showButtons && (
          <div className={`flex flex-col sm:flex-row ${alignment === 'center' ? 'justify-center' : ''} gap-4`}>
            <Link to="/register">
              <Button size="lg" className="bg-africa-green hover:bg-africa-earth text-white px-6 py-6 rounded-md font-medium">
                Join the Movement <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/whitepaper">
              <Button variant="outline" size="lg" className="bg-white/10 backdrop-blur-sm border-white hover:bg-white/20 text-white px-6 py-6 rounded-md font-medium">
                Read the Whitepaper <Download className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Hero;
