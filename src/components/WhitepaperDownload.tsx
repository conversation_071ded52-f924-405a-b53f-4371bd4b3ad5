
import React from "react";
import { Download, FileText, Calendar, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";

interface WhitepaperProps {
  version: string;
  title?: string;
  date: string;
  fileName: string;
  fileSize: string;
  filePath?: string;
  isLatest?: boolean;
  authors?: string[];
  summary?: string;
}

const WhitepaperDownload: React.FC<WhitepaperProps> = ({
  version,
  title,
  date,
  fileName,
  fileSize,
  filePath,
  isLatest = false,
  authors,
  summary,
}) => {
  const handleDownload = () => {
    // In a real application, this would trigger the file download
    // For now, we'll just show a notification
    if (filePath) {
      toast({
        title: "Download started",
        description: `${fileName} is being downloaded.`,
      });
      // window.open(filePath, "_blank");
    } else {
      toast({
        variant: "destructive",
        title: "Download failed",
        description: "File path not available. Please try again later.",
      });
    }
  };

  const displayTitle = title || `Resource-Based Economy Whitepaper ${version}`;

  return (
    <div className={`border ${isLatest ? "border-africa-green" : "border-gray-200"} rounded-lg p-6 mb-4 bg-white`}>
      <div className="flex flex-col md:flex-row md:items-start gap-4">
        <div className={`p-4 rounded-lg ${isLatest ? "bg-africa-green/10" : "bg-gray-100"} self-start`}>
          <FileText className={`${isLatest ? "text-africa-green" : "text-gray-500"} h-8 w-8`} />
        </div>
        
        <div className="flex-grow">
          <h3 className="font-medium text-lg flex items-center flex-wrap gap-2">
            {isLatest && <span className="bg-africa-green text-white text-xs px-2 py-0.5 rounded">LATEST</span>}
            {displayTitle}
          </h3>
          
          {authors && authors.length > 0 && (
            <p className="text-sm text-gray-600 mt-1">
              By {authors.join(", ")}
            </p>
          )}
          
          <div className="flex items-center text-sm text-gray-500 mt-2">
            <Calendar className="h-4 w-4 mr-1" />
            <span>{date}</span>
          </div>
          
          {summary && (
            <div className="mt-3 text-sm text-gray-700">
              {summary}
            </div>
          )}
          
          <div className="mt-4 flex flex-wrap gap-3">
            <Button 
              className={`${isLatest ? "bg-africa-green hover:bg-africa-earth" : "bg-gray-600 hover:bg-gray-700"} text-white`}
              onClick={handleDownload}
            >
              Download PDF <Download className="ml-2 h-4 w-4" />
            </Button>
            
            <Button variant="outline">
              Read Online <ExternalLink className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="text-right md:min-w-[100px]">
          <p className="text-xs text-gray-500">PDF • {fileSize}</p>
          <p className="text-xs text-gray-500 mt-1">Version {version}</p>
        </div>
      </div>
    </div>
  );
};

export default WhitepaperDownload;
