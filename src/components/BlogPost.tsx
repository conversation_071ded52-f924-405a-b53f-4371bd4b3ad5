
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Calendar, User, ArrowRight, Heart, MessageSquare, Bookmark, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface BlogPostProps {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  author: string;
  likes?: number;
  comments?: number;
  socialLinks?: {
    type: 'twitter' | 'facebook' | 'instagram' | 'youtube' | 'tiktok';
    url: string;
  }[];
  fullPost?: boolean;
}

const BlogPost: React.FC<BlogPostProps> = ({
  id,
  title,
  excerpt,
  image,
  date,
  author,
  likes = 0,
  comments = 0,
  socialLinks = [],
  fullPost = false
}) => {
  return (
    <article className="mb-12">
      <div className="relative h-64 mb-6 rounded-lg overflow-hidden">
        <img 
          src={image} 
          alt={title} 
          className="w-full h-full object-cover transition-transform hover:scale-110 duration-700"
        />
      </div>

      <div className="flex items-center mb-3 text-gray-500 text-sm">
        <div className="flex items-center mr-4">
          <Calendar size={14} className="mr-1" />
          <span>{date}</span>
        </div>
        <div className="flex items-center">
          <User size={14} className="mr-1" />
          <span>{author}</span>
        </div>
      </div>

      <h2 className="text-2xl font-bold mb-3 text-africa-black hover:text-africa-green transition">
        <Link to={`/blog/${id}`}>{title}</Link>
      </h2>

      <p className="text-gray-700 mb-4">
        {excerpt}
      </p>

      <div className="flex justify-between items-center mb-4">
        <div className="flex space-x-4">
          <Button variant="ghost" className="interaction-button text-gray-600 flex items-center gap-1 p-1">
            <Heart size={18} /> 
            <span>{likes}</span>
          </Button>
          <Button variant="ghost" className="interaction-button text-gray-600 flex items-center gap-1 p-1">
            <MessageSquare size={18} />
            <span>{comments}</span>
          </Button>
          <Button variant="ghost" className="interaction-button text-gray-600 p-1">
            <Bookmark size={18} />
          </Button>
          <Button variant="ghost" className="interaction-button text-gray-600 p-1">
            <Share2 size={18} />
          </Button>
        </div>
        
        {!fullPost && (
          <Link to={`/blog/${id}`}>
            <Button variant="ghost" className="text-africa-green hover:text-africa-earth flex items-center">
              Continue Reading <ArrowRight size={16} className="ml-2" />
            </Button>
          </Link>
        )}
      </div>

      {socialLinks.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-500 mb-2">Related Social Media</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {socialLinks.map((link, index) => (
              <a 
                key={index} 
                href={link.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="social-embed"
              >
                <div className={`h-12 bg-gray-100 flex items-center justify-center ${
                  link.type === 'twitter' ? 'bg-[#1DA1F2] text-white' : 
                  link.type === 'facebook' ? 'bg-[#4267B2] text-white' :
                  link.type === 'instagram' ? 'bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#FCAF45] text-white' :
                  link.type === 'youtube' ? 'bg-[#FF0000] text-white' :
                  'bg-[#000000] text-white'
                }`}>
                  <span className="text-sm font-medium">View on {link.type.charAt(0).toUpperCase() + link.type.slice(1)}</span>
                </div>
              </a>
            ))}
          </div>
        </div>
      )}
    </article>
  );
};

export default BlogPost;
