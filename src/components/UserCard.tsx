
import React from "react";
import { <PERSON>r, Heart, MessageSquare } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";

interface UserCardProps {
  name: string;
  expertise: string[];
  location: string;
  avatar?: string;
  bio: string;
  onConnect?: () => void;
  onDonate?: () => void;
  onMessage?: () => void;
}

const UserCard: React.FC<UserCardProps> = ({
  name,
  expertise,
  location,
  avatar,
  bio,
  onConnect,
  onDonate,
  onMessage
}) => {
  return (
    <Card className="overflow-hidden transition-shadow duration-200 hover:shadow-lg">
      <CardHeader className="flex flex-row items-center gap-4 p-4">
        <div className="h-16 w-16 rounded-full overflow-hidden bg-africa-green/20 flex items-center justify-center">
          {avatar ? (
            <img src={avatar} alt={name} className="h-full w-full object-cover" />
          ) : (
            <User className="h-8 w-8 text-africa-green" />
          )}
        </div>
        <div>
          <h3 className="font-semibold text-lg">{name}</h3>
          <div className="flex flex-wrap gap-1 mt-1">
            {expertise.map((skill, index) => (
              <span 
                key={index} 
                className="bg-africa-green/10 text-africa-green text-xs px-2 py-1 rounded-full"
              >
                {skill}
              </span>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-1">{location}</p>
        </div>
      </CardHeader>
      
      <CardContent className="px-4 pb-4">
        <p className="text-sm text-gray-700 line-clamp-3">{bio}</p>
      </CardContent>
      
      <CardFooter className="flex justify-between border-t p-4 pt-3 bg-gray-50">
        <Button 
          variant="outline" 
          size="sm" 
          className="text-xs" 
          onClick={onConnect}
        >
          Connect
        </Button>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="text-xs" 
            onClick={onMessage}
          >
            <MessageSquare className="h-3 w-3 mr-1" />
            Message
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="text-xs bg-africa-green text-white hover:bg-africa-earth border-none" 
            onClick={onDonate}
          >
            <Heart className="h-3 w-3 mr-1" />
            Donate
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default UserCard;
