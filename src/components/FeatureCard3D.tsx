
import React from "react";
import { LucideIcon } from "lucide-react";
import { motion } from 'framer-motion';

interface FeatureCard3DProps {
  title: string;
  description: string;
  icon: LucideIcon;
  color: string;
}

const FeatureCard3D: React.FC<FeatureCard3DProps> = ({ title, description, icon: Icon, color }) => {
  return (
    <motion.div
      className="bg-white rounded-lg p-6 shadow-lg card-hover relative overflow-hidden"
      whileHover={{ scale: 1.03, rotateY: 5, rotateX: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className={`${color} h-12 w-12 rounded-full flex items-center justify-center mb-5`}>
        <Icon className="text-white" size={24} />
      </div>
      <div className="before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent before:translate-x-[-100%] hover:before:animate-[shine_1.5s_ease-in-out]"></div>
      <h3 className="text-xl font-semibold mb-3">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </motion.div>
  );
};

export default FeatureCard3D;
