
import React from "react";
import { Calendar, User } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { formatDistanceToNow } from "date-fns";

interface BlogPostCardProps {
  id: string;
  title: string;
  excerpt: string;
  coverImage?: string;
  author: {
    name: string;
    avatar?: string;
  };
  category: string;
  publishedAt: Date;
  readTime?: number;
}

const BlogPostCard: React.FC<BlogPostCardProps> = ({
  id,
  title,
  excerpt,
  coverImage,
  author,
  category,
  publishedAt,
  readTime = 5,
}) => {
  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md h-full flex flex-col">
      {coverImage && (
        <Link to={`/blog/${id}`} className="block overflow-hidden h-48">
          <img 
            src={coverImage} 
            alt={title} 
            className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
          />
        </Link>
      )}
      
      <CardContent className="p-5 flex-grow">
        <div className="flex items-center justify-between mb-3">
          <span className="text-xs font-medium px-2 py-1 rounded-full bg-africa-green/10 text-africa-green">
            {category}
          </span>
          <div className="text-xs text-gray-500 flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {formatDistanceToNow(publishedAt, { addSuffix: true })}
          </div>
        </div>
        
        <Link to={`/blog/${id}`}>
          <h3 className="font-bold text-lg leading-tight mb-2 hover:text-africa-green transition-colors">
            {title}
          </h3>
        </Link>
        
        <p className="text-sm text-gray-600 line-clamp-3">
          {excerpt}
        </p>
      </CardContent>
      
      <CardFooter className="px-5 py-4 border-t flex items-center justify-between">
        <div className="flex items-center">
          <div className="h-7 w-7 rounded-full overflow-hidden bg-africa-green/20 flex items-center justify-center mr-2">
            {author.avatar ? (
              <img src={author.avatar} alt={author.name} className="h-full w-full object-cover" />
            ) : (
              <User className="h-4 w-4 text-africa-green" />
            )}
          </div>
          <span className="text-xs font-medium">{author.name}</span>
        </div>
        
        <span className="text-xs text-gray-500">{readTime} min read</span>
      </CardFooter>
    </Card>
  );
};

export default BlogPostCard;
