
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, User, LogOut } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import AuthModal from "@/components/AuthModal";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const { user, signOut } = useAuth();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <nav className="bg-white shadow-sm py-4 sticky top-0 z-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex justify-between items-center">
          <Link to="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-full african-gradient"></div>
            <span className="font-montserrat font-semibold text-lg text-africa-green">KENNECT</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-gray-700 hover:text-africa-green transition">About</Link>
            <Link to="/community" className="text-gray-700 hover:text-africa-green transition">Community</Link>
            <Link to="/resources" className="text-gray-700 hover:text-africa-green transition">Resources</Link>
            <Link to="/whitepaper" className="text-gray-700 hover:text-africa-green transition">Whitepaper</Link>
            <Link to="/blog" className="text-gray-700 hover:text-africa-green transition">Blog</Link>
            <Link to="/gallery" className="text-gray-700 hover:text-africa-green transition">Gallery</Link>
            <Link to="/visionary" className="text-gray-700 hover:text-africa-green transition">Visionary</Link>
            <Link to="/contact" className="text-gray-700 hover:text-africa-green transition">Contact</Link>

            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.user_metadata?.avatar_url} alt={user.user_metadata?.full_name} />
                      <AvatarFallback className="bg-africa-green text-white">
                        {user.user_metadata?.full_name?.charAt(0) || user.email?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">{user.user_metadata?.full_name || 'User'}</p>
                      <p className="w-[200px] truncate text-sm text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard" className="flex items-center">
                      <User className="mr-2 h-4 w-4" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                onClick={() => setAuthModalOpen(true)}
                className="bg-africa-green hover:bg-africa-earth text-white transition"
              >
                Join Kenya's Future
              </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button onClick={toggleMenu} className="text-gray-700">
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            <div className="flex flex-col space-y-4">
              <Link to="/" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>About</Link>
              <Link to="/community" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Community</Link>
              <Link to="/resources" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Resources</Link>
              <Link to="/whitepaper" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Whitepaper</Link>
              <Link to="/blog" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Blog</Link>
              <Link to="/gallery" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Gallery</Link>
              <Link to="/visionary" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Visionary</Link>
              <Link to="/contact" className="text-gray-700 hover:text-africa-green transition" onClick={toggleMenu}>Contact</Link>
              <Link to="/register" onClick={toggleMenu}>
                <Button className="bg-africa-green hover:bg-africa-earth text-white w-full transition">Join Kenya's Future</Button>
              </Link>
            </div>
          </div>
        )}
      </div>

      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
      />
    </nav>
  );
};

export default Navbar;
