import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Calendar, User, Play, Eye } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import ReactPlayer from 'react-player'

interface BlogCardProps {
  id: string
  title: string
  excerpt: string
  slug: string
  featuredImage?: string
  videoUrl?: string
  videoPlatform?: 'youtube' | 'tiktok' | 'instagram'
  category: {
    name: string
    color: string
    slug: string
  }
  author: {
    name: string
    avatar?: string
  }
  publishedAt: string
  readTime?: number
  index?: number
}

const BlogCard: React.FC<BlogCardProps> = ({
  id,
  title,
  excerpt,
  slug,
  featuredImage,
  videoUrl,
  videoPlatform,
  category,
  author,
  publishedAt,
  readTime = 5,
  index = 0
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getVideoPlatformIcon = () => {
    switch (videoPlatform) {
      case 'youtube':
        return '🎥'
      case 'tiktok':
        return '🎵'
      case 'instagram':
        return '📸'
      default:
        return '▶️'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="h-full"
    >
      <Card className="h-full overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
        {/* Media Section */}
        <div className="relative aspect-video overflow-hidden">
          {videoUrl ? (
            <div className="relative w-full h-full">
              <ReactPlayer
                url={videoUrl}
                width="100%"
                height="100%"
                light={featuredImage}
                playing={false}
                controls={false}
                className="react-player"
              />
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300 flex items-center justify-center">
                <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                  <Play className="text-africa-green" size={24} />
                </div>
              </div>
              <div className="absolute top-3 left-3">
                <Badge className="bg-africa-green/90 text-white border-0">
                  {getVideoPlatformIcon()} {videoPlatform?.toUpperCase()}
                </Badge>
              </div>
            </div>
          ) : featuredImage ? (
            <div className="relative w-full h-full">
              <img
                src={featuredImage}
                alt={title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-africa-green to-africa-earth flex items-center justify-center">
              <div className="text-white text-6xl opacity-20">📝</div>
            </div>
          )}
        </div>

        <CardHeader className="pb-3">
          {/* Category Badge */}
          <div className="flex items-center justify-between mb-2">
            <Badge 
              className="text-white border-0 text-xs font-medium"
              style={{ backgroundColor: category.color }}
            >
              {category.name}
            </Badge>
            {readTime && (
              <div className="flex items-center text-gray-500 text-xs">
                <Eye size={12} className="mr-1" />
                {readTime} min read
              </div>
            )}
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-africa-black line-clamp-2 group-hover:text-africa-green transition-colors duration-300">
            {title}
          </h3>
        </CardHeader>

        <CardContent className="pb-4">
          {/* Excerpt */}
          <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
            {excerpt}
          </p>
        </CardContent>

        <CardFooter className="pt-0 mt-auto">
          {/* Author and Date */}
          <div className="flex items-center justify-between w-full mb-4">
            <div className="flex items-center space-x-2">
              {author.avatar ? (
                <img
                  src={author.avatar}
                  alt={author.name}
                  className="w-6 h-6 rounded-full object-cover"
                />
              ) : (
                <div className="w-6 h-6 rounded-full bg-africa-green text-white flex items-center justify-center text-xs">
                  {author.name.charAt(0)}
                </div>
              )}
              <span className="text-sm text-gray-600">{author.name}</span>
            </div>
            <div className="flex items-center text-gray-500 text-xs">
              <Calendar size={12} className="mr-1" />
              {formatDate(publishedAt)}
            </div>
          </div>

          {/* Read More Button */}
          <Link to={`/blog/${slug}`} className="w-full">
            <Button 
              className="w-full bg-africa-green hover:bg-africa-earth text-white transition-all duration-300 group-hover:shadow-lg"
              size="sm"
            >
              Read More
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </motion.div>
  )
}

export default BlogCard
