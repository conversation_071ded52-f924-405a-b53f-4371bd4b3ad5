
import React, { useState } from "react";
import { CalendarClock, MoveRight, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";

interface TimelineItem {
  id: string;
  date: string;
  title: string;
  description: string;
  isCompleted: boolean;
  image?: string;
}

const Timeline = () => {
  // This would come from an API in a real application
  const [timelineItems] = useState<TimelineItem[]>([
    {
      id: "1",
      date: "March 2023",
      title: "Project Conception",
      description: "Initial idea and research on how to implement a Resource-Based Economy in Kenya and wider Africa.",
      isCompleted: true,
      image: "https://images.unsplash.com/photo-1611095566888-f1446042c8fc?auto=format&fit=crop&q=80"
    },
    {
      id: "2",
      date: "June 2023",
      title: "Website Launch",
      description: "Launch of the first version of the Kennect-Afrinnect website to gather initial supporters.",
      isCompleted: true,
      image: "https://images.unsplash.com/photo-1493397212122-2b85dda8106b?auto=format&fit=crop&q=80"
    },
    {
      id: "3",
      date: "October 2023",
      title: "First Community Meeting",
      description: "Virtual gathering of initial supporters to discuss vision and implementation strategies.",
      isCompleted: true
    },
    {
      id: "4",
      date: "January 2024",
      title: "Whitepaper Version 1.0",
      description: "Release of our comprehensive whitepaper detailing the vision, challenges, and implementation strategies.",
      isCompleted: true,
      image: "https://images.unsplash.com/photo-1456081445129-830eb8d4bfc6?auto=format&fit=crop&q=80"
    },
    {
      id: "5",
      date: "April 2024",
      title: "Partnership with Local Universities",
      description: "Collaborations established with universities in Kenya for research and development initiatives.",
      isCompleted: false
    },
    {
      id: "6",
      date: "July 2024",
      title: "First Physical Prototype",
      description: "Development of a small-scale prototype showcasing elements of circular city design.",
      isCompleted: false,
      image: "https://images.unsplash.com/photo-1615729947596-a598e5de0ab3?auto=format&fit=crop&q=80"
    },
    {
      id: "7",
      date: "December 2024",
      title: "International Conference",
      description: "Hosting our first international conference on Resource-Based Economy principles for Africa.",
      isCompleted: false
    }
  ]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <Hero 
        title="Our Journey" 
        subtitle="Follow our progress and milestones as we build Africa's resource-based future"
        showButtons={false}
        backgroundImage="https://images.unsplash.com/photo-1488342994276-7c3bc0742042?auto=format&fit=crop&q=80"
      />
      
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center mb-10">
              <CalendarClock className="h-10 w-10 text-africa-green mr-4" />
              <h2 className="text-3xl font-bold">Project Timeline</h2>
            </div>
            
            <div className="timeline-container">
              {timelineItems.map((item, index) => (
                <div key={item.id} className="timeline-item">
                  <div className={`ml-4 ${index === timelineItems.length - 1 ? 'pb-0' : 'pb-10'}`}>
                    <div className="flex items-center">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        item.isCompleted ? 'bg-africa-green text-white' : 'bg-gray-200 text-gray-700'
                      }`}>
                        {item.date}
                      </span>
                      {item.isCompleted && (
                        <span className="ml-2 text-xs text-africa-green font-medium">Completed</span>
                      )}
                    </div>
                    <h3 className="text-xl font-semibold mt-2">{item.title}</h3>
                    <p className="mt-1 text-gray-600">{item.description}</p>
                    {item.image && (
                      <div className="mt-4 rounded-md overflow-hidden h-48">
                        <img 
                          src={item.image} 
                          alt={item.title} 
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-12 text-center">
              <p className="text-lg text-gray-600 mb-6">
                Our timeline is constantly evolving as we make progress and hit new milestones. 
                Check back regularly for updates or join our community to get involved.
              </p>
              <Button className="bg-africa-green hover:bg-africa-earth text-white">
                Join Our Community <MoveRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>
      
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Upcoming Events</h2>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center w-12 h-12 mb-4 rounded-full bg-africa-green mx-auto">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Virtual Town Hall</h3>
                <p className="text-gray-600 mb-4">
                  Join us for a virtual discussion on implementing resource-based economic principles in urban planning.
                </p>
                <p className="text-africa-green font-medium">June 15, 2024 • 3:00 PM EAT</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center w-12 h-12 mb-4 rounded-full bg-africa-green mx-auto">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Innovation Workshop</h3>
                <p className="text-gray-600 mb-4">
                  A hands-on workshop focused on sustainable design technologies for African communities.
                </p>
                <p className="text-africa-green font-medium">July 22, 2024 • 10:00 AM EAT</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Timeline;
