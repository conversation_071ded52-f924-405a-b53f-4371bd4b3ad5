import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Filter, Grid, List } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import Hero from '@/components/Hero'
import ImageGallery from '@/components/ImageGallery'

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid')

  // Mock gallery data
  const galleries = [
    {
      id: '1',
      title: 'Circular City Designs',
      category: 'architecture',
      images: [
        {
          id: '1',
          url: '/venus/venus1.jpg',
          caption: 'Circular city overview - sustainable urban planning',
          alt_text: 'Aerial view of circular city design'
        },
        {
          id: '2',
          url: '/venus/venus2.jpg',
          caption: 'Central hub with integrated transportation',
          alt_text: 'Central transportation hub'
        },
        {
          id: '3',
          url: '/venus/venus3.jpg',
          caption: 'Residential areas with green spaces',
          alt_text: 'Green residential zones'
        },
        {
          id: '4',
          url: '/venus/venus4.jpg',
          caption: 'Industrial and research facilities',
          alt_text: 'Research and development areas'
        },
        {
          id: '5',
          url: '/venus/venus5.jpg',
          caption: 'Agricultural integration within the city',
          alt_text: 'Urban agriculture systems'
        },
        {
          id: '6',
          url: '/venus/venus6.jpg',
          caption: 'Renewable energy infrastructure',
          alt_text: 'Solar and wind energy systems'
        }
      ]
    },
    {
      id: '2',
      title: 'Kenya\'s Natural Resources',
      category: 'nature',
      images: [
        {
          id: '7',
          url: 'https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80',
          caption: 'Mount Kenya - Natural heritage',
          alt_text: 'Mount Kenya landscape'
        },
        {
          id: '8',
          url: 'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?auto=format&fit=crop&q=80',
          caption: 'Maasai Mara wildlife conservation',
          alt_text: 'Wildlife in Maasai Mara'
        },
        {
          id: '9',
          url: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?auto=format&fit=crop&q=80',
          caption: 'Great Rift Valley geological formations',
          alt_text: 'Rift Valley landscape'
        },
        {
          id: '10',
          url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80',
          caption: 'Coastal marine ecosystems',
          alt_text: 'Kenya coast marine life'
        }
      ]
    },
    {
      id: '3',
      title: 'Sustainable Technology',
      category: 'technology',
      images: [
        {
          id: '11',
          url: 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?auto=format&fit=crop&q=80',
          caption: 'Solar energy installations',
          alt_text: 'Solar panel arrays'
        },
        {
          id: '12',
          url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80',
          caption: 'Smart city infrastructure',
          alt_text: 'Digital city systems'
        },
        {
          id: '13',
          url: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80',
          caption: 'Automated resource management',
          alt_text: 'Automated systems'
        }
      ]
    },
    {
      id: '4',
      title: 'Community Development',
      category: 'community',
      images: [
        {
          id: '14',
          url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80',
          caption: 'Community collaboration spaces',
          alt_text: 'People working together'
        },
        {
          id: '15',
          url: 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?auto=format&fit=crop&q=80',
          caption: 'Educational facilities',
          alt_text: 'Learning environments'
        },
        {
          id: '16',
          url: 'https://images.unsplash.com/photo-1500937386664-56d1dfef3854?auto=format&fit=crop&q=80',
          caption: 'Sustainable agriculture practices',
          alt_text: 'Community farming'
        }
      ]
    }
  ]

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'architecture', label: 'Architecture & Design' },
    { value: 'nature', label: 'Natural Resources' },
    { value: 'technology', label: 'Technology' },
    { value: 'community', label: 'Community' }
  ]

  const filteredGalleries = selectedCategory === 'all' 
    ? galleries 
    : galleries.filter(gallery => gallery.category === selectedCategory)

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <Hero 
        title="Visual Gallery" 
        subtitle="Explore Kenya's resource-based future through stunning visuals and architectural designs"
        backgroundImage="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80"
      />

      {/* Filters */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">View:</span>
              <div className="flex border rounded-lg overflow-hidden">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-none"
                >
                  <Grid size={16} />
                </Button>
                <Button
                  variant={viewMode === "masonry" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("masonry")}
                  className="rounded-none"
                >
                  <List size={16} />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          {filteredGalleries.length > 0 ? (
            <div className="space-y-16">
              {filteredGalleries.map((gallery, index) => (
                <motion.div
                  key={gallery.id}
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2 }}
                >
                  <ImageGallery
                    images={gallery.images}
                    title={gallery.title}
                    className="mb-8"
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🖼️</div>
              <h3 className="text-xl font-semibold text-africa-black mb-2">No galleries found</h3>
              <p className="text-gray-600">No galleries match the selected category.</p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default Gallery
