import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Filter, Grid, List } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import Hero from '@/components/Hero'
import ImageGallery from '@/components/ImageGallery'

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid')

  // Mock gallery data
  const galleries = [
    {
      id: '1',
      title: 'Circular City Designs',
      category: 'architecture',
      images: [
        {
          id: '1',
          url: '/venus/venus1.jpg',
          caption: 'Circular city overview - sustainable urban planning',
          alt_text: 'Aerial view of circular city design'
        },
        {
          id: '2',
          url: '/venus/venus2.jpg',
          caption: 'Central hub with integrated transportation',
          alt_text: 'Central transportation hub'
        },
        {
          id: '3',
          url: '/venus/venus3.jpg',
          caption: 'Residential areas with green spaces',
          alt_text: 'Green residential zones'
        },
        {
          id: '4',
          url: '/venus/venus4.jpg',
          caption: 'Industrial and research facilities',
          alt_text: 'Research and development areas'
        },
        {
          id: '5',
          url: '/venus/venus5.jpg',
          caption: 'Agricultural integration within the city',
          alt_text: 'Urban agriculture systems'
        },
        {
          id: '6',
          url: '/venus/venus6.jpg',
          caption: 'Renewable energy infrastructure',
          alt_text: 'Solar and wind energy systems'
        }
      ]
    },
    {
      id: '2',
      title: 'Kenya\'s Natural Resources',
      category: 'nature',
      images: [
        {
          id: '7',
          url: 'https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80',
          caption: 'Mount Kenya - Natural heritage',
          alt_text: 'Mount Kenya landscape'
        },
        {
          id: '8',
          url: 'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?auto=format&fit=crop&q=80',
          caption: 'Maasai Mara wildlife conservation',
          alt_text: 'Wildlife in Maasai Mara'
        },
        {
          id: '9',
          url: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?auto=format&fit=crop&q=80',
          caption: 'Great Rift Valley geological formations',
          alt_text: 'Rift Valley landscape'
        },
        {
          id: '10',
          url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80',
          caption: 'Coastal marine ecosystems',
          alt_text: 'Kenya coast marine life'
        }
      ]
    },
    {
      id: '3',
      title: 'Sustainable Technology',
      category: 'technology',
      images: [
        {
          id: '11',
          url: 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?auto=format&fit=crop&q=80',
          caption: 'Solar energy installations',
          alt_text: 'Solar panel arrays'
        },
        {
          id: '12',
          url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80',
          caption: 'Smart city infrastructure',
          alt_text: 'Digital city systems'
        },
        {
          id: '13',
          url: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80',
          caption: 'Automated resource management',
          alt_text: 'Automated systems'
        }
      ]
    },
    {
      id: '4',
      title: 'Community Development',
      category: 'community',
      images: [
        {
          id: '14',
          url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80',
          caption: 'Community collaboration spaces',
          alt_text: 'People working together'
        },
        {
          id: '15',
          url: 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?auto=format&fit=crop&q=80',
          caption: 'Educational facilities',
          alt_text: 'Learning environments'
        },
        {
          id: '16',
          url: 'https://images.unsplash.com/photo-1500937386664-56d1dfef3854?auto=format&fit=crop&q=80',
          caption: 'Sustainable agriculture practices',
          alt_text: 'Community farming'
        }
      ]
    }
  ]

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'architecture', label: 'Architecture & Design' },
    { value: 'nature', label: 'Natural Resources' },
    { value: 'technology', label: 'Technology' },
    { value: 'community', label: 'Community' }
  ]

  const filteredGalleries = selectedCategory === 'all'
    ? galleries
    : galleries.filter(gallery => gallery.category === selectedCategory)

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <Hero
        title="Visual Gallery"
        subtitle="Explore Kenya's resource-based future through stunning visuals and architectural designs"
        backgroundImage="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80"
      />

      {/* Filters */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">View:</span>
              <div className="flex border rounded-lg overflow-hidden">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-none"
                >
                  <Grid size={16} />
                </Button>
                <Button
                  variant={viewMode === "masonry" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("masonry")}
                  className="rounded-none"
                >
                  <List size={16} />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Movie Gallery Style Grid */}
      <section className="py-16 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
        <div className="container mx-auto px-4">
          {filteredGalleries.length > 0 ? (
            <div className="space-y-20">
              {filteredGalleries.map((gallery, galleryIndex) => (
                <motion.div
                  key={gallery.id}
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: galleryIndex * 0.2 }}
                  className="relative"
                >
                  {/* Category Header */}
                  <div className="mb-8">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-1 h-8 bg-gradient-to-b from-africa-green to-africa-gold rounded-full"></div>
                      <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-white to-africa-gold bg-clip-text text-transparent">
                        {gallery.title}
                      </h2>
                    </div>
                    <div className="flex items-center gap-2 text-gray-400">
                      <span className="text-sm">{gallery.images.length} items</span>
                      <span>•</span>
                      <span className="text-sm capitalize">{gallery.category}</span>
                    </div>
                  </div>

                  {/* Movie-style horizontal scroll */}
                  <div className="relative group">
                    <div className="flex gap-4 overflow-x-auto scrollbar-hide pb-4" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                      {gallery.images.map((image, imageIndex) => (
                        <motion.div
                          key={image.id}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: (galleryIndex * 0.2) + (imageIndex * 0.1) }}
                          className="relative flex-shrink-0 group/item cursor-pointer"
                          style={{ width: '280px', height: '400px' }}
                        >
                          {/* Movie poster style card */}
                          <div className="relative w-full h-full rounded-xl overflow-hidden shadow-2xl transform transition-all duration-300 group-hover/item:scale-105 group-hover/item:z-10">
                            <img
                              src={image.url}
                              alt={image.alt_text || image.caption}
                              className="w-full h-full object-cover"
                            />

                            {/* Gradient overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"></div>

                            {/* Content overlay */}
                            <div className="absolute bottom-0 left-0 right-0 p-6 transform translate-y-full group-hover/item:translate-y-0 transition-transform duration-300">
                              <h3 className="text-white font-bold text-lg mb-2 line-clamp-2">
                                {image.caption || `Image ${imageIndex + 1}`}
                              </h3>
                              <p className="text-gray-300 text-sm">
                                {image.alt_text || 'Click to view full size'}
                              </p>
                            </div>

                            {/* Play button overlay */}
                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">
                              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M8 5v14l11-7z"/>
                                </svg>
                              </div>
                            </div>

                            {/* Category badge */}
                            <div className="absolute top-4 left-4">
                              <span className="px-3 py-1 bg-africa-green/80 backdrop-blur-sm rounded-full text-xs font-medium text-white">
                                {gallery.category}
                              </span>
                            </div>

                            {/* Quality badge */}
                            <div className="absolute top-4 right-4">
                              <span className="px-2 py-1 bg-africa-gold/80 backdrop-blur-sm rounded text-xs font-bold text-white">
                                HD
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* Scroll indicators */}
                    <div className="absolute top-1/2 -translate-y-1/2 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center cursor-pointer hover:bg-black/70 transition-colors">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </div>
                    </div>
                    <div className="absolute top-1/2 -translate-y-1/2 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center cursor-pointer hover:bg-black/70 transition-colors">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🎬</div>
              <h3 className="text-xl font-semibold text-white mb-2">No galleries found</h3>
              <p className="text-gray-400">No galleries match the selected category.</p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default Gallery
