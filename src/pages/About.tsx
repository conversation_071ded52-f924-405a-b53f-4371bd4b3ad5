
import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, Users, Target, Heart, Globe, Lightbulb, BookOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";

const About = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <Hero
        title="About Kennect-Afrinnect"
        subtitle="Building bridges to Kenya's sustainable future through community, innovation, and resource-based economics"
        backgroundImage="https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80"
      />

      {/* Introduction to The Venus Project */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-africa-black">The Venus Project: A Blueprint for the Future</h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Founded by visionary social engineer Jacque Fresco (1916-2017), The Venus Project presents a comprehensive plan
                for social redesign where human and environmental concerns are given precedence over monetary considerations.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <h3 className="text-3xl font-bold mb-6 text-africa-black">Jacque Fresco's Vision</h3>
                <p className="text-lg text-gray-700 mb-6">
                  For over 75 years, Jacque Fresco dedicated his life to developing solutions for a sustainable future.
                  His work spans architecture, industrial design, social engineering, and futurism, all unified by a
                  single goal: creating a world that works for everyone.
                </p>
                <p className="text-lg text-gray-700 mb-6">
                  Fresco's Resource-Based Economy eliminates the artificial scarcity created by monetary systems,
                  instead using Earth's abundant resources and advanced technology to provide a high standard of
                  living for all people while protecting the environment.
                </p>
                <div className="flex flex-wrap gap-3">
                  <Badge className="bg-africa-green text-white">Social Engineering</Badge>
                  <Badge className="bg-africa-earth text-white">Sustainable Design</Badge>
                  <Badge className="bg-africa-gold text-white">Systems Thinking</Badge>
                  <Badge className="bg-africa-sky text-white">Future Planning</Badge>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                className="relative"
              >
                <VideoPlayer
                  url="https://www.youtube.com/watch?v=T9c821s9mjw"
                  platform="youtube"
                  title="The Venus Project: Designing the Future"
                  className="rounded-lg overflow-hidden shadow-xl"
                />
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* What is a Resource-Based Economy */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-africa-black">What is a Resource-Based Economy?</h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                A comprehensive socio-economic system that transcends politics, poverty, money, and war by applying
                the scientific method to social concerns.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-africa-green h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Globe className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-africa-black">Earth's Common Heritage</h3>
                <p className="text-gray-700">
                  All of Earth's resources—land, water, air, minerals, and energy sources—are declared the common
                  heritage of all Earth's people, managed scientifically for the benefit of all life.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-africa-sky h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Zap className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-africa-black">Technology for Liberation</h3>
                <p className="text-gray-700">
                  Advanced technology and automation are used to eliminate scarcity, reduce human labor, and
                  provide abundance for all, rather than creating unemployment and inequality.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-africa-earth h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <BookOpen className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-africa-black">Scientific Method</h3>
                <p className="text-gray-700">
                  Decisions are made based on scientific research, environmental considerations, and human needs
                  rather than political opinions, traditions, or profit motives.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-africa-gold h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Users className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-africa-black">Human-Centered Design</h3>
                <p className="text-gray-700">
                  All systems, cities, and technologies are designed to enhance human potential, creativity, and
                  well-being while fostering cooperation and mutual aid.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.5 }}
                className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-africa-green h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Leaf className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-africa-black">Ecological Sustainability</h3>
                <p className="text-gray-700">
                  All human activities are designed to work in harmony with natural systems, ensuring the
                  long-term health of the planet and all its inhabitants.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.6 }}
                className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-africa-sunset h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Heart className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-africa-black">Holistic Education</h3>
                <p className="text-gray-700">
                  Education focuses on developing critical thinking, creativity, and emotional intelligence
                  rather than competition and conformity to outdated social patterns.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Circular Cities and Design */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-africa-black">Circular Cities: The Future of Urban Design</h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                Jacque Fresco's revolutionary circular city designs maximize efficiency, minimize environmental impact,
                and create optimal living conditions for all inhabitants.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <VideoPlayer
                  url="https://www.youtube.com/watch?v=Yb5ivvcTvRQ"
                  platform="youtube"
                  title="Circular City Design Principles"
                  className="rounded-lg overflow-hidden shadow-xl"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <h3 className="text-3xl font-bold mb-6 text-africa-black">Intelligent Urban Planning</h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="bg-africa-green rounded-full p-2 mr-4 mt-1">
                      <CheckCircle className="text-white" size={16} />
                    </div>
                    <div>
                      <h4 className="font-semibold text-africa-black mb-2">Efficient Resource Distribution</h4>
                      <p className="text-gray-700">Circular design minimizes transportation needs and maximizes resource efficiency through intelligent placement of facilities.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-africa-green rounded-full p-2 mr-4 mt-1">
                      <CheckCircle className="text-white" size={16} />
                    </div>
                    <div>
                      <h4 className="font-semibold text-africa-black mb-2">Integrated Agriculture</h4>
                      <p className="text-gray-700">Food production is integrated within the city using advanced hydroponic and aeroponic systems.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-africa-green rounded-full p-2 mr-4 mt-1">
                      <CheckCircle className="text-white" size={16} />
                    </div>
                    <div>
                      <h4 className="font-semibold text-africa-black mb-2">Renewable Energy Systems</h4>
                      <p className="text-gray-700">Solar, wind, geothermal, and other renewable sources provide clean energy for all city operations.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-africa-green rounded-full p-2 mr-4 mt-1">
                      <CheckCircle className="text-white" size={16} />
                    </div>
                    <div>
                      <h4 className="font-semibold text-africa-black mb-2">Waste-Free Design</h4>
                      <p className="text-gray-700">All materials are designed for complete recycling and reuse, eliminating waste and pollution.</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Kenya's Adaptation */}
      <section className="py-20 bg-gradient-to-r from-africa-green to-africa-earth text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">Adapting The Venus Project for Kenya</h2>
              <p className="text-xl max-w-4xl mx-auto opacity-90">
                Kenya's unique geography, resources, and cultural heritage provide an ideal foundation for implementing
                resource-based economic principles adapted to African contexts.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-8"
              >
                <div className="bg-white/20 h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Zap className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">Abundant Renewable Energy</h3>
                <p className="opacity-90">
                  Kenya's geothermal, solar, and wind resources can provide clean energy for the entire continent,
                  powering advanced manufacturing and urban systems.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-8"
              >
                <div className="bg-white/20 h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Users className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">Ubuntu Philosophy</h3>
                <p className="opacity-90">
                  Kenya's traditional Ubuntu philosophy of "I am because we are" aligns perfectly with resource-based
                  economic principles of cooperation and mutual aid.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-8"
              >
                <div className="bg-white/20 h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Globe className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">Strategic Location</h3>
                <p className="opacity-90">
                  Kenya's position as the gateway to East Africa makes it ideal for demonstrating sustainable
                  development models that can spread across the continent.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-8"
              >
                <div className="bg-white/20 h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Leaf className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">Biodiversity Wealth</h3>
                <p className="opacity-90">
                  Kenya's incredible biodiversity and ecosystems provide natural models for sustainable design
                  and biomimicry in technology and architecture.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.5 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-8"
              >
                <div className="bg-white/20 h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <BookOpen className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">Innovation Hub</h3>
                <p className="opacity-90">
                  Kenya's growing tech sector and innovation ecosystem provide the foundation for developing
                  advanced technologies needed for resource-based systems.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.6 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-8"
              >
                <div className="bg-white/20 h-16 w-16 rounded-full flex items-center justify-center mb-6">
                  <Heart className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">Young Population</h3>
                <p className="opacity-90">
                  Kenya's young, educated population is ready to embrace new paradigms and build the sustainable
                  future that previous generations could only dream of.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Jacque Fresco Quote */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="bg-white rounded-lg p-12 shadow-xl">
                <svg className="w-16 h-16 text-africa-green mx-auto mb-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
                <blockquote className="mb-8">
                  <p className="text-3xl md:text-4xl font-medium text-africa-black mb-6 leading-relaxed">
                    "We must stop abusing the Earth and start using it intelligently. We have the technology to provide
                    abundance for all people while protecting the environment. The only thing missing is the will to change."
                  </p>
                </blockquote>
                <div className="flex items-center justify-center">
                  <div className="text-center">
                    <cite className="text-xl font-semibold text-africa-green block">Jacque Fresco</cite>
                    <span className="text-gray-600">Founder of The Venus Project (1916-2017)</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Kenya's Natural Resources Video */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-africa-black">Kenya's Natural Abundance</h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                Discover the incredible natural resources and landscapes that make Kenya the perfect foundation
                for a sustainable, resource-based future.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <h3 className="text-3xl font-bold mb-6 text-africa-black">A Land of Infinite Potential</h3>
                <div className="space-y-6">
                  <p className="text-lg text-gray-700">
                    Kenya is blessed with extraordinary natural resources that, when managed scientifically and sustainably,
                    can provide abundance for all its people and serve as a model for the entire African continent.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-africa-green/10 rounded-lg p-4">
                      <h4 className="font-semibold text-africa-black mb-2">Geothermal Energy</h4>
                      <p className="text-gray-700 text-sm">World's largest untapped geothermal potential in the Rift Valley</p>
                    </div>

                    <div className="bg-africa-sky/10 rounded-lg p-4">
                      <h4 className="font-semibold text-africa-black mb-2">Solar Resources</h4>
                      <p className="text-gray-700 text-sm">300+ days of sunshine annually across most regions</p>
                    </div>

                    <div className="bg-africa-earth/10 rounded-lg p-4">
                      <h4 className="font-semibold text-africa-black mb-2">Biodiversity</h4>
                      <p className="text-gray-700 text-sm">25,000+ species in diverse ecosystems</p>
                    </div>

                    <div className="bg-africa-gold/10 rounded-lg p-4">
                      <h4 className="font-semibold text-africa-black mb-2">Water Resources</h4>
                      <p className="text-gray-700 text-sm">Major rivers, lakes, and aquifers for sustainable management</p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <VideoPlayer
                  url="https://www.youtube.com/watch?v=avbqagqJgbE"
                  platform="youtube"
                  title="Kenya's Natural Beauty and Resources"
                  className="rounded-lg overflow-hidden shadow-xl"
                />
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Roadmap */}
      <section className="py-20 bg-africa-black text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">Implementation Roadmap</h2>
              <p className="text-xl opacity-90 max-w-4xl mx-auto">
                A practical pathway for transitioning Kenya to a resource-based economy, building on existing strengths
                and addressing current challenges.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 }}
                className="text-center"
              >
                <div className="bg-africa-green h-20 w-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold">1</span>
                </div>
                <h3 className="text-xl font-bold mb-4">Education & Awareness</h3>
                <p className="opacity-90">
                  Comprehensive education programs to help Kenyans understand resource-based economic principles
                  and their benefits for sustainable development.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="text-center"
              >
                <div className="bg-africa-sky h-20 w-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold">2</span>
                </div>
                <h3 className="text-xl font-bold mb-4">Pilot Projects</h3>
                <p className="opacity-90">
                  Small-scale demonstration projects in renewable energy, sustainable agriculture,
                  and circular city design to prove concepts and build confidence.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="text-center"
              >
                <div className="bg-africa-earth h-20 w-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold">3</span>
                </div>
                <h3 className="text-xl font-bold mb-4">Infrastructure Development</h3>
                <p className="opacity-90">
                  Large-scale infrastructure projects including renewable energy grids,
                  sustainable transportation, and advanced manufacturing facilities.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4 }}
                className="text-center"
              >
                <div className="bg-africa-gold h-20 w-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold">4</span>
                </div>
                <h3 className="text-xl font-bold mb-4">Full Implementation</h3>
                <p className="opacity-90">
                  Complete transition to resource-based systems with abundance for all,
                  serving as a model for sustainable development across Africa.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Image Gallery Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-africa-black">Visualizing Kenya's Future</h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                Explore images that capture the essence of our vision for a sustainable, resource-based Kenya
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 }}
                className="relative group overflow-hidden rounded-xl shadow-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80"
                  alt="Mount Kenya - Natural Heritage"
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-lg font-bold">Mount Kenya</h3>
                    <p className="text-sm">Our Natural Heritage</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="relative group overflow-hidden rounded-xl shadow-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1466611653911-95081537e5b7?auto=format&fit=crop&q=80"
                  alt="Solar Energy in Kenya"
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-lg font-bold">Solar Energy</h3>
                    <p className="text-sm">Abundant Renewable Power</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="relative group overflow-hidden rounded-xl shadow-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80"
                  alt="Community Development"
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-lg font-bold">Community Unity</h3>
                    <p className="text-sm">Building Together</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4 }}
                className="relative group overflow-hidden rounded-xl shadow-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1500937386664-56d1dfef3854?auto=format&fit=crop&q=80"
                  alt="Sustainable Agriculture"
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-lg font-bold">Sustainable Agriculture</h3>
                    <p className="text-sm">Food Security for All</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.5 }}
                className="relative group overflow-hidden rounded-xl shadow-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80"
                  alt="Technology Innovation"
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-lg font-bold">Innovation Hub</h3>
                    <p className="text-sm">Technology for Good</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.6 }}
                className="relative group overflow-hidden rounded-xl shadow-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?auto=format&fit=crop&q=80"
                  alt="Wildlife Conservation"
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-lg font-bold">Wildlife Conservation</h3>
                    <p className="text-sm">Protecting Our Heritage</p>
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="text-center mt-12">
              <Link to="/gallery">
                <Button size="lg" className="bg-africa-green hover:bg-africa-earth text-white px-8 py-4">
                  View Full Gallery <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-africa-green to-africa-earth text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Join Kenya's Transformation</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto opacity-90">
              Be part of the movement to create a sustainable, abundant future for Kenya and all of Africa.
              Together, we can build the world that Jacque Fresco envisioned—adapted for our unique African context.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-6">
              <Link to="/register">
                <Button size="lg" className="bg-white text-africa-green hover:bg-gray-100 px-8 py-4 text-lg">
                  Join the Movement <ArrowRight className="ml-2 h-6 w-6" />
                </Button>
              </Link>
              <Link to="/blog">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg">
                  Read Our Research
                </Button>
              </Link>
              <Link to="/gallery">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg">
                  View Gallery
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;
