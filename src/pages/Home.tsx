import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>R<PERSON>, Users, Book, Heart, MessageSquare, User, Leaf } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import ResourceBasedScene from "@/components/3d/ResourceBasedScene";

const Home = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <Hero 
        title="KENNECT" 
        subtitle="Building a sustainable Resource-Based Economy for Kenya through connection, education and collaboration."
        backgroundImage="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80"
      />
      
      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Platform Features</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              KENNECT brings together individuals across Kenya to collaborate on building
              a sustainable Resource-Based Economy inspired by The Venus Project.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md border border-gray-100"
            >
              <div className="bg-africa-green h-14 w-14 rounded-full flex items-center justify-center mb-5">
                <Users className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Kenyan Network</h3>
              <p className="text-gray-600 mb-4">
                Connect with like-minded individuals across Kenya. Join specialized groups for farming, engineering, 
                medicine, tech, and culture.
              </p>
              <Link to="/register">
                <Button variant="link" className="text-africa-green p-0 h-auto font-medium">
                  Join Community <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md border border-gray-100"
            >
              <div className="bg-africa-earth h-14 w-14 rounded-full flex items-center justify-center mb-5">
                <Book className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Knowledge Hub</h3>
              <p className="text-gray-600 mb-4">
                Access blogs, articles, and resources about RBE principles, sustainability, and practical
                implementation strategies across Kenya's diverse regions.
              </p>
              <Link to="/blog">
                <Button variant="link" className="text-africa-earth p-0 h-auto font-medium">
                  Explore Knowledge <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md border border-gray-100"
            >
              <div className="bg-africa-gold h-14 w-14 rounded-full flex items-center justify-center mb-5">
                <Heart className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Support System</h3>
              <p className="text-gray-600 mb-4">
                Contribute to projects and initiatives through our donation system. Support innovators
                working on RBE solutions across Kenya.
              </p>
              <Link to="/visionary">
                <Button variant="link" className="text-africa-gold p-0 h-auto font-medium">
                  Support Vision <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md border border-gray-100"
            >
              <div className="bg-africa-sky h-14 w-14 rounded-full flex items-center justify-center mb-5">
                <MessageSquare className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Communication Tools</h3>
              <p className="text-gray-600 mb-4">
                Connect through our messaging system. Create group chats for projects, initiatives, 
                and collaborative efforts spanning across regions in Kenya.
              </p>
              <Link to="/contact">
                <Button variant="link" className="text-africa-sky p-0 h-auto font-medium">
                  Start Connecting <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md border border-gray-100"
            >
              <div className="bg-africa-navy h-14 w-14 rounded-full flex items-center justify-center mb-5">
                <User className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Expert Directory</h3>
              <p className="text-gray-600 mb-4">
                Find specialists in various fields ready to collaborate. Search for skills and resources
                available within our growing network in Kenya.
              </p>
              <Button variant="link" className="text-africa-navy p-0 h-auto font-medium">
                Coming Soon <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-africa-green to-africa-earth text-white rounded-lg p-6 shadow-md"
            >
              <div className="bg-white/20 h-14 w-14 rounded-full flex items-center justify-center mb-5">
                <Leaf className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Resource-Based Economy</h3>
              <p className="text-white/90 mb-4">
                Learn about how Resource-Based Economy principles can transform Kenyan communities
                and create sustainable prosperity for all.
              </p>
              <Link to="/whitepaper">
                <Button variant="link" className="text-white p-0 h-auto font-medium">
                  Read Whitepaper <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Kenya's Abundant Resources Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Kenya's Abundant Resources</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Reimagining how we utilize our country's natural wealth for the benefit of all.
            </p>
          </div>
          
          <ResourceBasedScene />
          
          <div className="mt-8 text-center">
            <Link to="/register">
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                Join Our Vision for Kenya
              </Button>
            </Link>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-900 via-green-700 to-green-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Build Kenya's Future?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Join visionaries, engineers, farmers, medical professionals, artists and change-makers
            who are already part of the KENNECT community in Kenya.
          </p>
          <Link to="/register">
            <Button size="lg" className="bg-white text-green-700 hover:bg-white/90">
              Create Your Account <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Home;
