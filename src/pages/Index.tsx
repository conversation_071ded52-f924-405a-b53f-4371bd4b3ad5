import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, Leaf, Globe, Users, LightbulbIcon, Recycle, Download, Calendar, User, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import ResourceBasedScene from "@/components/3d/ResourceBasedScene";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <Hero
        title="Kenya's Resource-Based Future"
        subtitle="Join the movement to build a sustainable Resource-Based Economy where Kenyan resources are designed to serve all people."
        backgroundImage="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&q=80"
      />

      {/* Kenya Map Section (non-3D) */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-africa-black">Reimagining Kenya</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our vision for a resource-based future in Kenya that can serve as a model for sustainable development.
            </p>
          </div>

          <div className="flex justify-center">
            <img
              src="https://images.unsplash.com/photo-1535912040073-2d0ed7e63b5d?auto=format&fit=crop&q=80"
              alt="Kenya Map"
              className="rounded-lg shadow-lg max-w-full md:max-w-2xl"
            />
          </div>

          <div className="mt-8 text-center">
            <Link to="/about">
              <Button className="bg-africa-green hover:bg-africa-earth text-white">
                Learn About Our Vision
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why a Resource-Based Economy?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our current economic systems are unsustainable and creating artificial scarcity.
              It's time for a new approach that benefits all of Kenya and its people.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md card-hover"
            >
              <div className="bg-africa-green h-12 w-12 rounded-full flex items-center justify-center mb-5">
                <Leaf className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Sustainable Living</h3>
              <p className="text-gray-600">
                Designs and technologies that work with Kenya's natural environments, ensuring resources for future generations.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md card-hover"
            >
              <div className="bg-africa-sky h-12 w-12 rounded-full flex items-center justify-center mb-5">
                <Globe className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Resource Management</h3>
              <p className="text-gray-600">
                Intelligent management of Kenya's abundant resources as the common heritage of all its people.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md card-hover"
            >
              <div className="bg-africa-earth h-12 w-12 rounded-full flex items-center justify-center mb-5">
                <Users className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Community Cooperation</h3>
              <p className="text-gray-600">
                Building on Kenya's strong community traditions to create systems of cooperation for collective well-being.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md card-hover"
            >
              <div className="bg-africa-gold h-12 w-12 rounded-full flex items-center justify-center mb-5">
                <LightbulbIcon className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Technology & Innovation</h3>
              <p className="text-gray-600">
                Leveraging appropriate technology to create abundance rather than unemployment and scarcity.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg p-6 shadow-md card-hover"
            >
              <div className="bg-africa-green h-12 w-12 rounded-full flex items-center justify-center mb-5">
                <Recycle className="text-white" size={24} />
              </div>
              <h3 className="text-xl font-semibold mb-3">Circular Design</h3>
              <p className="text-gray-600">
                Products and cities designed for durability, upgradeability, and complete recycling at the end of their lifecycle.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-africa-green to-africa-gold text-white rounded-lg p-6 shadow-md flex flex-col justify-between"
            >
              <div>
                <h3 className="text-xl font-semibold mb-3">Ready to contribute?</h3>
                <p className="mb-4">Join other Kenyans already working to make this vision a reality.</p>
              </div>
              <Link to="/register">
                <Button className="bg-white text-africa-green hover:bg-opacity-90 w-full">
                  Join the Movement <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Resource Scene (non-3D) */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Kenya's Abundant Resources</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Reimagining how we utilize our country's natural wealth for the benefit of all.
            </p>
          </div>

          <ResourceBasedScene />
        </div>
      </section>

      {/* Circular City (non-3D) */}
      <section className="py-20 bg-gray-50 circular-city">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-12 items-center">
            <div className="lg:w-1/2">
              <img
                src="/venus/venus1.jpg"
                alt="Circular City Design"
                className="rounded-lg shadow-xl w-full"
              />
            </div>
            <div className="lg:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Circular Cities: The Future of Kenyan Urban Design</h2>
              <p className="text-lg text-gray-700 mb-6">
                Inspired by Jacque Fresco's circular city designs, we're adapting this revolutionary approach to urban planning for Kenyan contexts, integrating with local environments and cultures.
              </p>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-africa-green text-white flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-4 w-4">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-lg text-gray-700">Efficient resource distribution minimizing waste and transportation costs</p>
                </li>
                <li className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-africa-green text-white flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-4 w-4">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-lg text-gray-700">Integrated agricultural systems producing fresh food within urban environments</p>
                </li>
                <li className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-africa-green text-white flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-4 w-4">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-lg text-gray-700">Renewable energy systems utilizing Kenya's abundant solar and geothermal resources</p>
                </li>
                <li className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-africa-green text-white flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-4 w-4">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-lg text-gray-700">Community spaces designed to foster connection, creativity, and collaboration</p>
                </li>
              </ul>
              <div className="mt-8">
                <Link to="/about">
                  <Button className="bg-africa-green hover:bg-africa-earth text-white">
                    Learn More About Circular Cities
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Preview Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-africa-black">Latest Insights & Research</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Stay updated with the latest developments, research, and stories about building Kenya's resource-based future.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {/* Featured Blog Post 1 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="aspect-video overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80"
                  alt="Building Sustainable Communities"
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <Badge className="mb-3 bg-africa-green text-white">Community Development</Badge>
                <h3 className="text-xl font-bold mb-3 text-africa-black hover:text-africa-green transition-colors">
                  <Link to="/blog/building-sustainable-communities-kenya">
                    Building Sustainable Communities in Kenya
                  </Link>
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  Exploring how resource-based economics can transform local communities across Kenya, creating sustainable and equitable living conditions for all.
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <User size={14} className="mr-1" />
                    Dr. Amina Kone
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    8 min read
                  </div>
                </div>
                <Link to="/blog/building-sustainable-communities-kenya">
                  <Button className="w-full bg-africa-green hover:bg-africa-earth text-white">
                    Read More
                  </Button>
                </Link>
              </div>
            </motion.div>

            {/* Featured Blog Post 2 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="aspect-video overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80"
                  alt="Technology for Resource Management"
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <Badge className="mb-3 bg-blue-500 text-white">Technology</Badge>
                <h3 className="text-xl font-bold mb-3 text-africa-black hover:text-africa-green transition-colors">
                  <Link to="/blog/technology-resource-management">
                    Technology for Resource Management
                  </Link>
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  How modern technology can help us better manage and distribute Kenya's natural resources for maximum benefit to all citizens.
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <User size={14} className="mr-1" />
                    Prof. James Mwangi
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    6 min read
                  </div>
                </div>
                <Link to="/blog/technology-resource-management">
                  <Button className="w-full bg-africa-green hover:bg-africa-earth text-white">
                    Read More
                  </Button>
                </Link>
              </div>
            </motion.div>

            {/* Featured Blog Post 3 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="aspect-video overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?auto=format&fit=crop&q=80"
                  alt="Circular Cities"
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <Badge className="mb-3 bg-amber-600 text-white">Urban Planning</Badge>
                <h3 className="text-xl font-bold mb-3 text-africa-black hover:text-africa-green transition-colors">
                  <Link to="/blog/circular-cities-urban-planning">
                    Circular Cities: The Future of Urban Planning
                  </Link>
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  Designing cities that work in harmony with nature, inspired by Jacque Fresco's vision but adapted for Kenyan contexts.
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <User size={14} className="mr-1" />
                    Arch. Sarah Wanjiku
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    10 min read
                  </div>
                </div>
                <Link to="/blog/circular-cities-urban-planning">
                  <Button className="w-full bg-africa-green hover:bg-africa-earth text-white">
                    Read More
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          {/* View All Blog Posts Button */}
          <div className="text-center">
            <Link to="/blog">
              <Button size="lg" className="bg-africa-green hover:bg-africa-earth text-white px-8">
                View All Articles <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Whitepaper Preview */}
      <section className="py-20 bg-gradient-to-r from-africa-black via-africa-green to-africa-earth text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Living Whitepaper</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            The KENNECT Whitepaper is constantly evolving with new research,
            insights, and practical approaches to building Kenya's resource-based future.
          </p>
          <div className="bg-white/10 backdrop-blur-sm p-8 rounded-lg max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">Latest Version: 1.0</h3>
            <p className="mb-6">
              Our initial whitepaper outlines the core principles, challenges, and implementation strategies
              for transitioning to a Resource-Based Economy in the Kenyan context.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link to="/whitepaper">
                <Button className="bg-white text-africa-green hover:bg-white/90">
                  Read Online
                </Button>
              </Link>
              <Link to="/whitepaper">
                <Button variant="outline" className="bg-transparent border-white hover:bg-white/10">
                  Download PDF <Download className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Call to action */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Help Build Kenya's Future?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            We need engineers, architects, scientists, educators, communicators, and visionaries
            who want to contribute their skills to this national initiative.
          </p>
          <Link to="/register">
            <Button size="lg" className="bg-africa-green hover:bg-africa-earth text-white px-8 py-6">
              Join the Movement <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
