
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, Download, FileText, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import <PERSON> from "@/components/Hero";
import WhitepaperDownload from "@/components/WhitepaperDownload";

const Whitepaper = () => {
  const [activeTab, setActiveTab] = useState("read");
  
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <Hero 
        title="Living Whitepaper" 
        subtitle="Our continuously evolving guide to building a Resource-Based Economy"
        showButtons={false}
      />

      {/* Tabs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab("read")}
              className={`py-4 px-6 font-medium text-lg ${
                activeTab === "read"
                  ? "border-b-2 border-rbe-blue text-rbe-blue"
                  : "text-gray-500"
              }`}
            >
              <BookOpen className="h-5 w-5 inline mr-2" />
              Read Online
            </button>
            <button
              onClick={() => setActiveTab("download")}
              className={`py-4 px-6 font-medium text-lg ${
                activeTab === "download"
                  ? "border-b-2 border-rbe-blue text-rbe-blue"
                  : "text-gray-500"
              }`}
            >
              <Download className="h-5 w-5 inline mr-2" />
              Download
            </button>
          </div>
        </div>
      </section>

      {activeTab === "read" ? (
        // Read Online Content
        <section className="py-12 bg-rbe-silver">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-md">
              <h1 className="text-3xl font-bold mb-8 text-rbe-navy">Resource-Based Economy Whitepaper</h1>
              <div className="prose prose-lg max-w-none">
                <h2>1. Introduction</h2>
                <p>
                  The global challenges we face today—climate change, wealth inequality, resource depletion, 
                  technological unemployment, and social unrest—are not isolated problems but symptoms of 
                  an economic system designed for an era that no longer exists.
                </p>
                <p>
                  This whitepaper presents a comprehensive vision for transitioning to a Resource-Based Economy (RBE), 
                  a socio-economic system in which all goods and services are available without the use of money, 
                  credits, barter, or any other system of debt or servitude.
                </p>
                
                <h2>2. The Problem with Current Economic Systems</h2>
                <p>
                  Our current monetary-based economic systems were developed during times of actual scarcity 
                  and limited technological capabilities. Today, we face different challenges:
                </p>
                <ul>
                  <li>Technological unemployment through automation</li>
                  <li>Environmental degradation from profit-driven resource extraction</li>
                  <li>Artificial scarcity created to maintain prices</li>
                  <li>Wealth concentration that undermines democracy</li>
                  <li>Financial incentives that reward harmful behaviors</li>
                </ul>
                
                <h2>3. Principles of a Resource-Based Economy</h2>
                <p>
                  A Resource-Based Economy operates on fundamentally different principles:
                </p>
                <ul>
                  <li>Earth's resources are the common heritage of all people</li>
                  <li>Science and technology are applied to benefit all humanity</li>
                  <li>Decisions are based on scientific understanding rather than profit motives</li>
                  <li>Products are designed for durability, efficiency, and recyclability</li>
                  <li>Human needs and environmental health are the primary metrics of success</li>
                </ul>
                
                <h2>4. Technical Implementation</h2>
                <p>
                  Transitioning to an RBE requires several key technological and infrastructural developments:
                </p>
                <ul>
                  <li>Global resource survey and management systems</li>
                  <li>Automation and AI applied to basic production and distribution</li>
                  <li>Clean energy systems to provide abundant power</li>
                  <li>Circular manufacturing processes that eliminate waste</li>
                  <li>Urban redesign for efficiency and high quality of life</li>
                </ul>
                
                <h2>5. Social Implications</h2>
                <p>
                  An RBE would transform social structures and human relationships:
                </p>
                <ul>
                  <li>Education focused on creativity, problem-solving, and collaboration</li>
                  <li>Healthcare centered on prevention and universal access</li>
                  <li>Work becoming voluntary and purpose-driven rather than survival-based</li>
                  <li>Communities designed to foster connection and cooperation</li>
                  <li>Cultural shift toward measuring success by contribution rather than accumulation</li>
                </ul>
                
                <h2>6. Transition Strategy</h2>
                <p>
                  Moving from current systems to an RBE requires a multiphased approach:
                </p>
                <ol>
                  <li>Building awareness and community through education and demonstration projects</li>
                  <li>Developing and open-sourcing key technologies for resource management and production</li>
                  <li>Creating pilot communities that implement RBE principles locally</li>
                  <li>Scaling successful models while adapting to diverse cultural and geographic contexts</li>
                  <li>Global coordination of resource management systems</li>
                </ol>
                
                <h2>7. Immediate Next Steps</h2>
                <p>
                  For individuals interested in advancing this vision:
                </p>
                <ul>
                  <li>Join the Future Society Initiative as a contributor</li>
                  <li>Start or join local groups focused on implementing RBE principles</li>
                  <li>Contribute professional skills to development of key technologies</li>
                  <li>Participate in educational outreach and advocacy</li>
                  <li>Support demonstration projects that showcase RBE concepts</li>
                </ul>
                
                <h2>8. Conclusion</h2>
                <p>
                  The transition to a Resource-Based Economy represents not merely an economic shift but an 
                  evolution in human civilization—moving from scarcity-based competition to abundance-based 
                  cooperation. The technology to create this world exists today; what we need now is the 
                  vision and determination to implement it.
                </p>
                <p>
                  By joining the Future Society Initiative, you become part of a global community working 
                  to make this vision a reality. Together, we can build a world beyond war, poverty, and 
                  environmental degradation—a world worthy of our technological and moral potential.
                </p>
              </div>
              
              <div className="mt-12 border-t pt-8 text-center">
                <Link to="/register">
                  <Button className="bg-rbe-blue hover:bg-rbe-navy text-white">
                    Join the Movement <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      ) : (
        // Download Content
        <section className="py-12 bg-rbe-silver">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold mb-8">Available Versions</h2>
              
              <WhitepaperDownload
                version="v1.0"
                date="April 15, 2024"
                fileName="rbe-whitepaper-v1.0.pdf"
                fileSize="2.4 MB"
                isLatest={true}
              />
              
              <WhitepaperDownload
                version="v0.9 (Beta)"
                date="January 30, 2024"
                fileName="rbe-whitepaper-beta.pdf"
                fileSize="2.1 MB"
              />
              
              <WhitepaperDownload
                version="v0.8 (Draft)"
                date="November 12, 2023"
                fileName="rbe-whitepaper-draft.pdf"
                fileSize="1.8 MB"
              />
              
              <div className="bg-white rounded-lg p-6 mt-8 border border-gray-200">
                <h3 className="text-xl font-medium mb-4">Request Printed Copies</h3>
                <p className="text-gray-700 mb-4">
                  For educational purposes, community groups, or libraries, we offer printed versions of our whitepaper at cost.
                </p>
                <Link to="/contact">
                  <Button variant="outline" className="border-rbe-blue text-rbe-blue hover:bg-rbe-blue/5">
                    Contact Us for Printed Copies
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      )}
      
      <Footer />
    </div>
  );
};

export default Whitepaper;
