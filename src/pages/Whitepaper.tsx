
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, Download, FileText, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import <PERSON> from "@/components/Hero";
import WhitepaperDownload from "@/components/WhitepaperDownload";

const Whitepaper = () => {
  const [activeTab, setActiveTab] = useState("read");

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <Hero
        title="Living Whitepaper"
        subtitle="Our continuously evolving guide to building a Resource-Based Economy"
        showButtons={false}
      />

      {/* Tabs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab("read")}
              className={`py-4 px-6 font-medium text-lg ${
                activeTab === "read"
                  ? "border-b-2 border-rbe-blue text-rbe-blue"
                  : "text-gray-500"
              }`}
            >
              <BookOpen className="h-5 w-5 inline mr-2" />
              Read Online
            </button>
            <button
              onClick={() => setActiveTab("download")}
              className={`py-4 px-6 font-medium text-lg ${
                activeTab === "download"
                  ? "border-b-2 border-rbe-blue text-rbe-blue"
                  : "text-gray-500"
              }`}
            >
              <Download className="h-5 w-5 inline mr-2" />
              Download
            </button>
          </div>
        </div>
      </section>

      {activeTab === "read" ? (
        // Read Online Content
        <section className="py-12 bg-gradient-to-br from-gray-50 to-blue-50">
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">
              {/* Header Section */}
              <div className="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-africa-green to-africa-earth rounded-full mb-4">
                    <FileText className="w-8 h-8 text-white" />
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-africa-green to-africa-earth bg-clip-text text-transparent">
                    Resource-Based Economy Whitepaper
                  </h1>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    A comprehensive guide to building Kenya's sustainable future through resource-based economic principles
                  </p>
                  <div className="flex items-center justify-center gap-4 mt-6 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <BookOpen className="w-4 h-4" />
                      Version 1.0
                    </span>
                    <span>•</span>
                    <span>Last updated: April 2024</span>
                    <span>•</span>
                    <span>15 min read</span>
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 border border-gray-100">
                <div className="prose prose-lg prose-blue max-w-none">
                  <div className="mb-12">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-8 h-8 bg-gradient-to-r from-africa-green to-africa-earth rounded-full flex items-center justify-center text-white font-bold">1</div>
                      <h2 className="text-3xl font-bold text-gray-800">Introduction</h2>
                    </div>
                    <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6 mb-6">
                      <p className="text-lg leading-relaxed text-gray-700">
                        The global challenges we face today—climate change, wealth inequality, resource depletion,
                        technological unemployment, and social unrest—are not isolated problems but symptoms of
                        an economic system designed for an era that no longer exists.
                      </p>
                    </div>
                    <p className="text-lg leading-relaxed text-gray-700">
                      This whitepaper presents a comprehensive vision for transitioning to a Resource-Based Economy (RBE),
                      a socio-economic system in which all goods and services are available without the use of money,
                      credits, barter, or any other system of debt or servitude.
                    </p>
                  </div>

                  <div className="mb-12">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-8 h-8 bg-gradient-to-r from-africa-green to-africa-earth rounded-full flex items-center justify-center text-white font-bold">2</div>
                      <h2 className="text-3xl font-bold text-gray-800">The Problem with Current Economic Systems</h2>
                    </div>
                    <p className="text-lg leading-relaxed text-gray-700 mb-6">
                      Our current monetary-based economic systems were developed during times of actual scarcity
                      and limited technological capabilities. Today, we face different challenges:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                        <h4 className="font-semibold text-red-800 mb-2">Technological unemployment through automation</h4>
                        <p className="text-red-700 text-sm">Jobs are being eliminated faster than new ones are created</p>
                      </div>
                      <div className="bg-orange-50 border-l-4 border-orange-400 p-4 rounded-r-lg">
                        <h4 className="font-semibold text-orange-800 mb-2">Environmental degradation</h4>
                        <p className="text-orange-700 text-sm">Profit-driven resource extraction damages ecosystems</p>
                      </div>
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                        <h4 className="font-semibold text-yellow-800 mb-2">Artificial scarcity</h4>
                        <p className="text-yellow-700 text-sm">Scarcity created to maintain prices and profits</p>
                      </div>
                      <div className="bg-purple-50 border-l-4 border-purple-400 p-4 rounded-r-lg">
                        <h4 className="font-semibold text-purple-800 mb-2">Wealth concentration</h4>
                        <p className="text-purple-700 text-sm">Growing inequality undermines democratic institutions</p>
                      </div>
                    </div>
                  </div>

                  <div className="mb-12">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-8 h-8 bg-gradient-to-r from-africa-green to-africa-earth rounded-full flex items-center justify-center text-white font-bold">3</div>
                      <h2 className="text-3xl font-bold text-gray-800">Principles of a Resource-Based Economy</h2>
                    </div>
                    <p className="text-lg leading-relaxed text-gray-700 mb-6">
                      A Resource-Based Economy operates on fundamentally different principles:
                    </p>
                    <div className="space-y-4">
                      <div className="flex items-start gap-4 p-4 bg-green-50 rounded-lg border border-green-200">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-semibold text-green-800 mb-1">Earth's resources are the common heritage of all people</h4>
                          <p className="text-green-700 text-sm">No individual or corporation owns natural resources; they belong to all humanity</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-semibold text-blue-800 mb-1">Science and technology are applied to benefit all humanity</h4>
                          <p className="text-blue-700 text-sm">Technology serves human needs rather than profit maximization</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-semibold text-purple-800 mb-1">Decisions are based on scientific understanding</h4>
                          <p className="text-purple-700 text-sm">Evidence-based decision making replaces profit-driven choices</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
                        <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-semibold text-orange-800 mb-1">Products are designed for durability and recyclability</h4>
                          <p className="text-orange-700 text-sm">Circular design eliminates waste and planned obsolescence</p>
                        </div>
                      </div>
                    </div>
                  </div>

                <h2>4. Technical Implementation</h2>
                <p>
                  Transitioning to an RBE requires several key technological and infrastructural developments:
                </p>
                <ul>
                  <li>Global resource survey and management systems</li>
                  <li>Automation and AI applied to basic production and distribution</li>
                  <li>Clean energy systems to provide abundant power</li>
                  <li>Circular manufacturing processes that eliminate waste</li>
                  <li>Urban redesign for efficiency and high quality of life</li>
                </ul>

                <h2>5. Social Implications</h2>
                <p>
                  An RBE would transform social structures and human relationships:
                </p>
                <ul>
                  <li>Education focused on creativity, problem-solving, and collaboration</li>
                  <li>Healthcare centered on prevention and universal access</li>
                  <li>Work becoming voluntary and purpose-driven rather than survival-based</li>
                  <li>Communities designed to foster connection and cooperation</li>
                  <li>Cultural shift toward measuring success by contribution rather than accumulation</li>
                </ul>

                <h2>6. Transition Strategy</h2>
                <p>
                  Moving from current systems to an RBE requires a multiphased approach:
                </p>
                <ol>
                  <li>Building awareness and community through education and demonstration projects</li>
                  <li>Developing and open-sourcing key technologies for resource management and production</li>
                  <li>Creating pilot communities that implement RBE principles locally</li>
                  <li>Scaling successful models while adapting to diverse cultural and geographic contexts</li>
                  <li>Global coordination of resource management systems</li>
                </ol>

                <h2>7. Immediate Next Steps</h2>
                <p>
                  For individuals interested in advancing this vision:
                </p>
                <ul>
                  <li>Join the Future Society Initiative as a contributor</li>
                  <li>Start or join local groups focused on implementing RBE principles</li>
                  <li>Contribute professional skills to development of key technologies</li>
                  <li>Participate in educational outreach and advocacy</li>
                  <li>Support demonstration projects that showcase RBE concepts</li>
                </ul>

                <h2>8. Conclusion</h2>
                <p>
                  The transition to a Resource-Based Economy represents not merely an economic shift but an
                  evolution in human civilization—moving from scarcity-based competition to abundance-based
                  cooperation. The technology to create this world exists today; what we need now is the
                  vision and determination to implement it.
                </p>
                <p>
                  By joining the Future Society Initiative, you become part of a global community working
                  to make this vision a reality. Together, we can build a world beyond war, poverty, and
                  environmental degradation—a world worthy of our technological and moral potential.
                </p>
              </div>

              <div className="mt-12 border-t pt-8 text-center">
                <Link to="/register">
                  <Button className="bg-rbe-blue hover:bg-rbe-navy text-white">
                    Join the Movement <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      ) : (
        // Download Content
        <section className="py-12 bg-rbe-silver">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold mb-8">Available Versions</h2>

              <WhitepaperDownload
                version="v1.0"
                date="April 15, 2024"
                fileName="rbe-whitepaper-v1.0.pdf"
                fileSize="2.4 MB"
                isLatest={true}
              />

              <WhitepaperDownload
                version="v0.9 (Beta)"
                date="January 30, 2024"
                fileName="rbe-whitepaper-beta.pdf"
                fileSize="2.1 MB"
              />

              <WhitepaperDownload
                version="v0.8 (Draft)"
                date="November 12, 2023"
                fileName="rbe-whitepaper-draft.pdf"
                fileSize="1.8 MB"
              />

              <div className="bg-white rounded-lg p-6 mt-8 border border-gray-200">
                <h3 className="text-xl font-medium mb-4">Request Printed Copies</h3>
                <p className="text-gray-700 mb-4">
                  For educational purposes, community groups, or libraries, we offer printed versions of our whitepaper at cost.
                </p>
                <Link to="/contact">
                  <Button variant="outline" className="border-rbe-blue text-rbe-blue hover:bg-rbe-blue/5">
                    Contact Us for Printed Copies
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      )}

      <Footer />
    </div>
  );
};

export default Whitepaper;
