
import React from "react";
import { Link } from "react-router-dom";
import { 
  Bell, FileText, Calendar, Settings, Users, BookOpen, 
  BarChart, Download, ExternalLink 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const Dashboard = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <div className="bg-rbe-navy text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-3xl font-bold mb-2">Welcome, User</h1>
              <p className="text-blue-200">Member since April 2024</p>
            </div>
            <div className="mt-4 md:mt-0">
              <Button className="bg-rbe-blue hover:bg-opacity-80">
                <Bell size={16} className="mr-2" /> Notifications
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex-grow bg-rbe-silver py-10">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <div className="lg:w-1/4">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6 bg-gradient-to-r from-rbe-blue to-rbe-navy text-white">
                  <div className="flex items-center space-x-4">
                    <div className="h-16 w-16 rounded-full bg-white text-rbe-navy flex items-center justify-center font-bold text-xl">
                      US
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold">User Name</h2>
                      <p className="text-blue-100">San Francisco, USA</p>
                    </div>
                  </div>
                </div>
                
                <nav className="p-4">
                  <ul className="space-y-2">
                    <li>
                      <a href="#" className="flex items-center p-3 bg-rbe-blue/10 text-rbe-blue rounded-md">
                        <BarChart size={18} className="mr-3" /> Dashboard
                      </a>
                    </li>
                    <li>
                      <a href="#" className="flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-md">
                        <FileText size={18} className="mr-3" /> Documents
                      </a>
                    </li>
                    <li>
                      <a href="#" className="flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-md">
                        <Calendar size={18} className="mr-3" /> Events
                      </a>
                    </li>
                    <li>
                      <a href="#" className="flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-md">
                        <Users size={18} className="mr-3" /> My Network
                      </a>
                    </li>
                    <li>
                      <a href="#" className="flex items-center p-3 text-gray-700 hover:bg-gray-100 rounded-md">
                        <Settings size={18} className="mr-3" /> Settings
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="lg:w-3/4">
              {/* Latest Updates */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-2xl font-semibold mb-4">Latest Updates</h2>
                
                <div className="space-y-4">
                  <div className="p-4 border-l-4 border-rbe-green bg-green-50">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium">New Whitepaper Version Available</h3>
                      <span className="text-sm text-gray-500">2 days ago</span>
                    </div>
                    <p className="text-gray-700 mt-1">Version 1.0 of our Resource-Based Economy whitepaper has been published.</p>
                    <div className="mt-2">
                      <Link to="/whitepaper" className="text-rbe-blue hover:underline text-sm inline-flex items-center">
                        Read or Download <ExternalLink size={14} className="ml-1" />
                      </Link>
                    </div>
                  </div>
                  
                  <div className="p-4 border-l-4 border-rbe-blue bg-blue-50">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium">Upcoming Virtual Workshop</h3>
                      <span className="text-sm text-gray-500">5 days ago</span>
                    </div>
                    <p className="text-gray-700 mt-1">Join us for a workshop on "Designing Sustainable Resource Systems" next Friday.</p>
                    <div className="mt-2">
                      <a href="#" className="text-rbe-blue hover:underline text-sm inline-flex items-center">
                        Register Now <ExternalLink size={14} className="ml-1" />
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Resources */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-2xl font-semibold mb-4">Resources & Materials</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4 flex items-center">
                    <div className="p-3 bg-blue-100 rounded-lg mr-3">
                      <BookOpen size={24} className="text-rbe-blue" />
                    </div>
                    <div>
                      <h3 className="font-medium">Orientation Guide</h3>
                      <p className="text-sm text-gray-500">Getting started with the initiative</p>
                    </div>
                    <Button variant="ghost" size="icon" className="ml-auto">
                      <Download size={18} />
                    </Button>
                  </div>
                  
                  <div className="border rounded-lg p-4 flex items-center">
                    <div className="p-3 bg-green-100 rounded-lg mr-3">
                      <FileText size={24} className="text-rbe-green" />
                    </div>
                    <div>
                      <h3 className="font-medium">Outreach Materials</h3>
                      <p className="text-sm text-gray-500">Presentations and handouts</p>
                    </div>
                    <Button variant="ghost" size="icon" className="ml-auto">
                      <Download size={18} />
                    </Button>
                  </div>
                  
                  <div className="border rounded-lg p-4 flex items-center">
                    <div className="p-3 bg-purple-100 rounded-lg mr-3">
                      <Users size={24} className="text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Team Directory</h3>
                      <p className="text-sm text-gray-500">Connect with other members</p>
                    </div>
                    <Button variant="ghost" size="icon" className="ml-auto">
                      <ExternalLink size={18} />
                    </Button>
                  </div>
                  
                  <div className="border rounded-lg p-4 flex items-center">
                    <div className="p-3 bg-orange-100 rounded-lg mr-3">
                      <Calendar size={24} className="text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Event Calendar</h3>
                      <p className="text-sm text-gray-500">Upcoming workshops and meetings</p>
                    </div>
                    <Button variant="ghost" size="icon" className="ml-auto">
                      <ExternalLink size={18} />
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Profile Completion */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-semibold">Profile Completion</h2>
                  <span className="bg-rbe-blue text-white px-2 py-1 rounded text-sm">75%</span>
                </div>
                
                <div className="w-full bg-gray-200 h-2 rounded-full mb-4">
                  <div className="bg-rbe-blue h-2 rounded-full" style={{ width: "75%" }}></div>
                </div>
                
                <p className="text-gray-700 mb-4">
                  Complete your profile to help us connect you with relevant projects and teams.
                </p>
                
                <ul className="space-y-2 mb-6">
                  <li className="flex items-center text-gray-700">
                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Basic Information
                  </li>
                  <li className="flex items-center text-gray-700">
                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Skills & Expertise
                  </li>
                  <li className="flex items-center text-gray-700">
                    <svg className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01" />
                    </svg>
                    Portfolio & Experience
                  </li>
                  <li className="flex items-center text-gray-700">
                    <svg className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01" />
                    </svg>
                    Availability & Preferences
                  </li>
                </ul>
                
                <Button className="bg-rbe-blue hover:bg-rbe-navy text-white">
                  Complete Your Profile
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Dashboard;
