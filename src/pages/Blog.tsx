
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Search, Filter, Grid, List } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import BlogCard from "@/components/BlogCard";
import CategoryFilter from "@/components/CategoryFilter";
import Fuse from 'fuse.js';

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("newest");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filteredPosts, setFilteredPosts] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(2); // Start on page 2
  const [postsPerPage] = useState(6);

  // Mock blog posts data with enhanced features
  const blogPosts = [
    {
      id: "1",
      title: "Building Sustainable Communities in Kenya",
      slug: "building-sustainable-communities-kenya",
      excerpt: "Exploring how resource-based economics can transform local communities across Kenya, creating sustainable and equitable living conditions for all.",
      content: "Full article content here...",
      author: { name: "Dr. Amina Kone", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
      publishedAt: "2024-01-15",
      category: { id: "1", name: "Community Development", slug: "community", color: "#008751" },
      readTime: 8,
      featuredImage: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80",
      videoUrl: "https://www.youtube.com/watch?v=T9c821s9mjw",
      videoPlatform: "youtube" as const,
      tags: ["sustainability", "community", "development"]
    },
    {
      id: "2",
      title: "Technology for Resource Management",
      slug: "technology-resource-management",
      excerpt: "How modern technology can help us better manage and distribute Kenya's natural resources for maximum benefit to all citizens.",
      content: "Full article content here...",
      author: { name: "Prof. James Mwangi", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" },
      publishedAt: "2024-01-10",
      category: { id: "2", name: "Technology", slug: "technology", color: "#4682B4" },
      readTime: 6,
      featuredImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80",
      tags: ["technology", "resources", "management"]
    },
    {
      id: "3",
      title: "Circular Cities: The Future of Urban Planning",
      slug: "circular-cities-urban-planning",
      excerpt: "Designing cities that work in harmony with nature, inspired by Jacque Fresco's vision but adapted for Kenyan contexts.",
      content: "Full article content here...",
      author: { name: "Arch. Sarah Wanjiku", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
      publishedAt: "2024-01-05",
      category: { id: "3", name: "Urban Planning", slug: "urban", color: "#A0522D" },
      readTime: 10,
      featuredImage: "https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?auto=format&fit=crop&q=80",
      videoUrl: "https://www.youtube.com/watch?v=Yb5ivvcTvRQ",
      videoPlatform: "youtube" as const,
      tags: ["urban planning", "cities", "design"]
    },
    {
      id: "4",
      title: "Education in a Resource-Based Economy",
      slug: "education-resource-based-economy",
      excerpt: "Reimagining education systems to prepare Kenyan youth for a future where creativity and collaboration drive progress.",
      content: "Full article content here...",
      author: { name: "Dr. Peter Kimani", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
      publishedAt: "2024-01-01",
      category: { id: "4", name: "Education", slug: "education", color: "#F2C12E" },
      readTime: 7,
      featuredImage: "https://images.unsplash.com/photo-1497486751825-1233686d5d80?auto=format&fit=crop&q=80",
      tags: ["education", "youth", "future"]
    },
    {
      id: "5",
      title: "Renewable Energy Solutions for Kenya",
      slug: "renewable-energy-solutions-kenya",
      excerpt: "Harnessing Kenya's abundant solar, wind, and geothermal resources to create a sustainable energy future for all.",
      content: "Full article content here...",
      author: { name: "Eng. Grace Mutua", avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150" },
      publishedAt: "2023-12-28",
      category: { id: "5", name: "Energy", slug: "energy", color: "#FF7F50" },
      readTime: 9,
      featuredImage: "https://images.unsplash.com/photo-1466611653911-95081537e5b7?auto=format&fit=crop&q=80",
      videoUrl: "https://www.youtube.com/watch?v=KphWsnhZ4Ag",
      videoPlatform: "youtube" as const,
      tags: ["energy", "renewable", "solar"]
    },
    {
      id: "6",
      title: "Agriculture in the New Economy",
      slug: "agriculture-new-economy",
      excerpt: "Transforming Kenyan agriculture through sustainable practices and technology to ensure food security for all.",
      content: "Full article content here...",
      author: { name: "Dr. Mary Njeri", avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150" },
      publishedAt: "2023-12-25",
      category: { id: "6", name: "Agriculture", slug: "agriculture", color: "#DAA520" },
      readTime: 5,
      featuredImage: "https://images.unsplash.com/photo-1500937386664-56d1dfef3854?auto=format&fit=crop&q=80",
      tags: ["agriculture", "food security", "sustainability"]
    },
    {
      id: "7",
      title: "Water Management in Arid Regions",
      slug: "water-management-arid-regions",
      excerpt: "Innovative solutions for water conservation and management in Kenya's arid and semi-arid lands.",
      content: "Full article content here...",
      author: { name: "Eng. David Kiprotich", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
      publishedAt: "2023-12-20",
      category: { id: "2", name: "Technology", slug: "technology", color: "#4682B4" },
      readTime: 7,
      featuredImage: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?auto=format&fit=crop&q=80",
      tags: ["water", "conservation", "technology"]
    },
    {
      id: "8",
      title: "Youth Empowerment Through Innovation",
      slug: "youth-empowerment-innovation",
      excerpt: "How Kenya's youth are driving innovation and creating solutions for tomorrow's challenges.",
      content: "Full article content here...",
      author: { name: "Grace Wanjiku", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
      publishedAt: "2023-12-15",
      category: { id: "4", name: "Education", slug: "education", color: "#F2C12E" },
      readTime: 6,
      featuredImage: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&q=80",
      videoUrl: "https://www.tiktok.com/@example/video/youth-innovation",
      videoPlatform: "tiktok" as const,
      tags: ["youth", "innovation", "education"]
    },
    {
      id: "9",
      title: "Renewable Energy Grid Integration",
      slug: "renewable-energy-grid-integration",
      excerpt: "Building a smart grid system that integrates solar, wind, and geothermal energy across Kenya.",
      content: "Full article content here...",
      author: { name: "Dr. Samuel Mutua", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" },
      publishedAt: "2023-12-10",
      category: { id: "5", name: "Energy", slug: "energy", color: "#FF7F50" },
      readTime: 9,
      featuredImage: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?auto=format&fit=crop&q=80",
      tags: ["renewable energy", "smart grid", "infrastructure"]
    },
    {
      id: "10",
      title: "Coastal Conservation Initiatives",
      slug: "coastal-conservation-initiatives",
      excerpt: "Protecting Kenya's marine ecosystems while supporting coastal communities through sustainable practices.",
      content: "Full article content here...",
      author: { name: "Dr. Fatuma Hassan", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
      publishedAt: "2023-12-05",
      category: { id: "1", name: "Community Development", slug: "community", color: "#008751" },
      readTime: 8,
      featuredImage: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80",
      videoUrl: "https://www.instagram.com/p/coastal-conservation/",
      videoPlatform: "instagram" as const,
      tags: ["conservation", "marine", "coastal communities"]
    },
    {
      id: "11",
      title: "Digital Literacy for Rural Communities",
      slug: "digital-literacy-rural-communities",
      excerpt: "Bridging the digital divide by bringing technology education to Kenya's rural areas.",
      content: "Full article content here...",
      author: { name: "Prof. Catherine Nyong'o", avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150" },
      publishedAt: "2023-11-30",
      category: { id: "4", name: "Education", slug: "education", color: "#F2C12E" },
      readTime: 6,
      featuredImage: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?auto=format&fit=crop&q=80",
      tags: ["digital literacy", "rural development", "education"]
    },
    {
      id: "12",
      title: "Sustainable Transportation Networks",
      slug: "sustainable-transportation-networks",
      excerpt: "Designing efficient, eco-friendly transportation systems for Kenya's growing urban centers.",
      content: "Full article content here...",
      author: { name: "Eng. Robert Kimani", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
      publishedAt: "2023-11-25",
      category: { id: "3", name: "Urban Planning", slug: "urban", color: "#A0522D" },
      readTime: 7,
      featuredImage: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?auto=format&fit=crop&q=80",
      tags: ["transportation", "urban planning", "sustainability"]
    }
  ];

  const categories = [
    { id: "1", name: "Community Development", slug: "community", color: "#008751", count: 1 },
    { id: "2", name: "Technology", slug: "technology", color: "#4682B4", count: 1 },
    { id: "3", name: "Urban Planning", slug: "urban", color: "#A0522D", count: 1 },
    { id: "4", name: "Education", slug: "education", color: "#F2C12E", count: 1 },
    { id: "5", name: "Energy", slug: "energy", color: "#FF7F50", count: 1 },
    { id: "6", name: "Agriculture", slug: "agriculture", color: "#DAA520", count: 1 }
  ];

  // Initialize Fuse.js for search
  const fuse = new Fuse(blogPosts, {
    keys: ['title', 'excerpt', 'tags', 'author.name'],
    threshold: 0.3,
  });

  useEffect(() => {
    let filtered = [...blogPosts];

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(post => post.category.id === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const searchResults = fuse.search(searchQuery);
      filtered = searchResults.map(result => result.item);
    }

    // Sort posts
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
        case "oldest":
          return new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime();
        case "title":
          return a.title.localeCompare(b.title);
        case "readTime":
          return a.readTime - b.readTime;
        default:
          return 0;
      }
    });

    setFilteredPosts(filtered);
  }, [selectedCategory, searchQuery, sortBy]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPosts = filteredPosts.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <Hero
        title="KENNECT Blog"
        subtitle="Insights, research, and stories about building Kenya's resource-based future"
        backgroundImage="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80"
      />

      {/* Enhanced Filters Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          {/* Search and Controls */}
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <Input
                placeholder="Search articles, authors, or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 border-africa-green/20 focus:border-africa-green"
              />
            </div>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full lg:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="title">Title A-Z</SelectItem>
                <SelectItem value="readTime">Read Time</SelectItem>
              </SelectContent>
            </Select>

            {/* View Mode */}
            <div className="flex border rounded-lg overflow-hidden">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-none"
              >
                <Grid size={16} />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-none"
              >
                <List size={16} />
              </Button>
            </div>
          </div>

          {/* Category Filter */}
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
          />
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          {/* Results Info */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-africa-black">
                {selectedCategory
                  ? `${categories.find(c => c.id === selectedCategory)?.name} Articles`
                  : 'All Articles'
                }
              </h2>
              <p className="text-gray-600 mt-1">
                {filteredPosts.length} article{filteredPosts.length !== 1 ? 's' : ''} found
                {searchQuery && ` for "${searchQuery}"`}
              </p>
            </div>
          </div>

          {/* Blog Posts */}
          {currentPosts.length > 0 ? (
            <>
              <div className={`grid gap-8 ${
                viewMode === "grid"
                  ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                  : "grid-cols-1"
              }`}>
                {currentPosts.map((post, index) => (
                <BlogCard
                  key={post.id}
                  id={post.id}
                  title={post.title}
                  excerpt={post.excerpt}
                  slug={post.slug}
                  featuredImage={post.featuredImage}
                  videoUrl={post.videoUrl}
                  videoPlatform={post.videoPlatform}
                  category={post.category}
                  author={post.author}
                  publishedAt={post.publishedAt}
                  readTime={post.readTime}
                  index={index}
                />
              ))}
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-center items-center space-x-2 mt-12"
                >
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="border-africa-green text-africa-green hover:bg-africa-green hover:text-white"
                  >
                    Previous
                  </Button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      onClick={() => handlePageChange(page)}
                      className={`w-10 h-10 ${
                        currentPage === page
                          ? 'bg-africa-green text-white'
                          : 'border-africa-green text-africa-green hover:bg-africa-green hover:text-white'
                      }`}
                    >
                      {page}
                    </Button>
                  ))}

                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="border-africa-green text-africa-green hover:bg-africa-green hover:text-white"
                  >
                    Next
                  </Button>
                </motion.div>
              )}
            </>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-africa-black mb-2">No articles found</h3>
              <p className="text-gray-600 mb-6">
                {searchQuery
                  ? `No articles match your search for "${searchQuery}"`
                  : "No articles in this category yet"
                }
              </p>
              <Button
                onClick={() => {
                  setSearchQuery("");
                  setSelectedCategory(null);
                }}
                className="bg-africa-green hover:bg-africa-earth text-white"
              >
                Clear Filters
              </Button>
            </motion.div>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-africa-green text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Get the latest insights and updates about Kenya's resource-based future delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-white/70"
              />
              <Button className="bg-white text-africa-green hover:bg-white/90">
                Subscribe
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Blog;
