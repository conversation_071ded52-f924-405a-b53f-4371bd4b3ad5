import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Calendar, User, Clock, ArrowLeft, Share2, Heart, MessageCircle, Tag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import VideoPlayer from '@/components/VideoPlayer'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  excerpt: string
  author: {
    name: string
    avatar?: string
    bio?: string
  }
  publishedAt: string
  category: {
    id: string
    name: string
    color: string
  }
  readTime: number
  featuredImage?: string
  videoUrl?: string
  videoPlatform?: 'youtube' | 'tiktok' | 'instagram'
  tags: string[]
  likes: number
  comments: number
}

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>()
  const { user } = useAuth()
  const [post, setPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [liked, setLiked] = useState(false)

  // Mock blog posts data - in production, this would come from API
  const mockPosts: BlogPost[] = [
    {
      id: "1",
      title: "Building Sustainable Communities in Kenya",
      slug: "building-sustainable-communities-kenya",
      content: `
        <h2>Introduction</h2>
        <p>Kenya stands at a crossroads. With a growing population and increasing urbanization, the need for sustainable community development has never been more critical. The resource-based economy model offers a revolutionary approach to creating equitable, sustainable communities that work in harmony with our natural environment.</p>

        <h2>The Current Challenge</h2>
        <p>Traditional economic models have led to inequality, environmental degradation, and unsustainable resource consumption. In Kenya, we see this in the form of:</p>
        <ul>
          <li>Rapid urbanization without proper infrastructure</li>
          <li>Depletion of natural resources</li>
          <li>Growing wealth inequality</li>
          <li>Environmental challenges including deforestation and water scarcity</li>
        </ul>

        <h2>The Resource-Based Solution</h2>
        <p>A resource-based economy focuses on the intelligent management of Earth's resources for the benefit of all. In the Kenyan context, this means:</p>

        <h3>1. Sustainable Urban Planning</h3>
        <p>Designing cities that work with nature, not against it. This includes circular city designs that minimize waste and maximize efficiency.</p>

        <h3>2. Community Cooperation</h3>
        <p>Building on Kenya's strong tradition of harambee (community cooperation) to create systems where everyone contributes according to their ability and receives according to their needs.</p>

        <h3>3. Technology Integration</h3>
        <p>Leveraging appropriate technology to solve local problems while creating opportunities for innovation and creativity.</p>

        <h2>Implementation Strategies</h2>
        <p>The transition to sustainable communities requires a multi-faceted approach:</p>

        <h3>Education and Awareness</h3>
        <p>Communities must understand the benefits and methods of sustainable living. This includes education about renewable energy, sustainable agriculture, and resource conservation.</p>

        <h3>Infrastructure Development</h3>
        <p>Building the physical infrastructure needed for sustainable communities, including renewable energy systems, water management, and waste processing facilities.</p>

        <h3>Economic Transition</h3>
        <p>Gradually shifting from profit-driven models to resource-based systems that prioritize human welfare and environmental sustainability.</p>

        <h2>Case Studies</h2>
        <p>Several pilot projects in Kenya are already demonstrating the potential of this approach:</p>

        <h3>Kibera Sustainable Housing Project</h3>
        <p>A community-led initiative that has transformed part of Kibera slum into a model sustainable community with solar power, rainwater harvesting, and community gardens.</p>

        <h3>Machakos County Water Project</h3>
        <p>A resource-based approach to water management that has brought clean water to over 50,000 people while restoring the local ecosystem.</p>

        <h2>The Path Forward</h2>
        <p>Building sustainable communities in Kenya is not just possible—it's essential for our future. By embracing resource-based principles and working together, we can create communities that are:</p>
        <ul>
          <li>Environmentally sustainable</li>
          <li>Socially equitable</li>
          <li>Economically viable</li>
          <li>Culturally appropriate</li>
        </ul>

        <p>The journey begins with each of us understanding our role in this transformation and taking action in our own communities.</p>

        <h2>Conclusion</h2>
        <p>Kenya has the opportunity to lead Africa and the world in demonstrating how resource-based economics can create truly sustainable communities. With our rich natural resources, strong community traditions, and innovative spirit, we can build a future where all Kenyans thrive in harmony with our environment.</p>

        <p>Join us in this vital mission. Together, we can build the Kenya of tomorrow—today.</p>
      `,
      excerpt: "Exploring how resource-based economics can transform local communities across Kenya, creating sustainable and equitable living conditions for all.",
      author: {
        name: "Dr. Amina Kone",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
        bio: "Dr. Amina Kone is a sustainable development expert with over 15 years of experience in community development across East Africa."
      },
      publishedAt: "2024-01-15",
      category: { id: "1", name: "Community Development", color: "#008751" },
      readTime: 8,
      featuredImage: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80",
      videoUrl: "https://www.youtube.com/watch?v=T9c821s9mjw",
      videoPlatform: "youtube",
      tags: ["sustainability", "community", "development", "kenya"],
      likes: 124,
      comments: 18
    },
    {
      id: "2",
      title: "Technology for Resource Management",
      slug: "technology-resource-management",
      content: `
        <h2>The Digital Revolution in Resource Management</h2>
        <p>Technology is transforming how we understand, monitor, and manage natural resources. In Kenya, innovative digital solutions are helping communities make better decisions about their environment and resources.</p>

        <h2>Smart Monitoring Systems</h2>
        <p>IoT sensors and satellite technology now allow us to monitor:</p>
        <ul>
          <li>Water quality and availability</li>
          <li>Soil health and agricultural productivity</li>
          <li>Forest cover and biodiversity</li>
          <li>Air quality in urban areas</li>
        </ul>

        <h2>AI-Powered Decision Making</h2>
        <p>Artificial intelligence helps analyze complex data patterns to optimize resource allocation and predict future needs.</p>

        <h2>Community Engagement Platforms</h2>
        <p>Digital platforms enable communities to participate in resource management decisions and share local knowledge.</p>
      `,
      excerpt: "How modern technology can help us better manage and distribute Kenya's natural resources for maximum benefit to all citizens.",
      author: {
        name: "Prof. James Mwangi",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150",
        bio: "Professor of Information Technology at University of Nairobi, specializing in sustainable technology solutions."
      },
      publishedAt: "2024-01-10",
      category: { id: "2", name: "Technology", color: "#4682B4" },
      readTime: 6,
      featuredImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80",
      tags: ["technology", "resources", "management", "AI"],
      likes: 89,
      comments: 12
    }
  ]

  useEffect(() => {
    // Simulate API call
    const foundPost = mockPosts.find(p => p.slug === slug)
    setPost(foundPost || null)
    setLoading(false)
  }, [slug])

  const handleLike = () => {
    if (!user) {
      toast.error('Please login to like posts')
      return
    }
    setLiked(!liked)
    toast.success(liked ? 'Post unliked' : 'Post liked!')
  }

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href)
    toast.success('Link copied to clipboard!')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-africa-green"></div>
        </div>
        <Footer />
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-africa-black mb-4">Post Not Found</h1>
            <p className="text-gray-600 mb-8">The blog post you're looking for doesn't exist.</p>
            <Link to="/blog">
              <Button className="bg-africa-green hover:bg-africa-earth text-white">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Blog
              </Button>
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-africa-black via-africa-green to-africa-earth text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto text-center"
          >
            <Badge
              className="mb-4 text-white border-0"
              style={{ backgroundColor: post.category.color }}
            >
              {post.category.name}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
              {post.title}
            </h1>
            <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
              {post.excerpt}
            </p>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center justify-center gap-6 text-white/80">
              <div className="flex items-center">
                <Avatar className="h-8 w-8 mr-3">
                  <AvatarImage src={post.author.avatar} alt={post.author.name} />
                  <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span>{post.author.name}</span>
              </div>
              <div className="flex items-center">
                <Calendar size={16} className="mr-2" />
                {new Date(post.publishedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
              <div className="flex items-center">
                <Clock size={16} className="mr-2" />
                {post.readTime} min read
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="mb-8"
            >
              <Link to="/blog">
                <Button variant="ghost" className="text-africa-green hover:bg-africa-green/10">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Blog
                </Button>
              </Link>
            </motion.div>

            {/* Featured Image */}
            {post.featuredImage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <img
                  src={post.featuredImage}
                  alt={post.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg shadow-xl"
                />
              </motion.div>
            )}

            {/* Video Player */}
            {post.videoUrl && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <VideoPlayer
                  url={post.videoUrl}
                  platform={post.videoPlatform}
                  title={post.title}
                />
              </motion.div>
            )}

            {/* Article Content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="prose prose-lg max-w-none mb-12"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            {/* Tags */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <div className="flex items-center gap-2 mb-4">
                <Tag size={16} className="text-gray-500" />
                <span className="text-gray-500 font-medium">Tags:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="bg-africa-green/10 text-africa-green">
                    {tag}
                  </Badge>
                ))}
              </div>
            </motion.div>

            <Separator className="my-8" />

            {/* Engagement Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="flex items-center justify-between mb-8"
            >
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={handleLike}
                  className={`flex items-center gap-2 ${liked ? 'text-red-500' : 'text-gray-500'} hover:text-red-500`}
                >
                  <Heart size={20} className={liked ? 'fill-current' : ''} />
                  {post.likes + (liked ? 1 : 0)}
                </Button>
                <Button variant="ghost" className="flex items-center gap-2 text-gray-500 hover:text-africa-green">
                  <MessageCircle size={20} />
                  {post.comments}
                </Button>
              </div>
              <Button
                variant="ghost"
                onClick={handleShare}
                className="flex items-center gap-2 text-gray-500 hover:text-africa-green"
              >
                <Share2 size={20} />
                Share
              </Button>
            </motion.div>

            {/* Author Bio */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-gray-50 rounded-lg p-6"
            >
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={post.author.avatar} alt={post.author.name} />
                  <AvatarFallback className="bg-africa-green text-white text-xl">
                    {post.author.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-xl font-semibold text-africa-black mb-2">
                    About {post.author.name}
                  </h3>
                  <p className="text-gray-600">
                    {post.author.bio}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default BlogPost
