
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Heart, ArrowRight, Download, Edit, Plus, Star, Users, Calendar, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import AuthModal from "@/components/AuthModal";
import { useAuth } from "@/contexts/AuthContext";

const Visionary = () => {
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [postModalOpen, setPostModalOpen] = useState(false);
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* Enhanced Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background with overlay */}
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1469041797191-50ace28483c3?auto=format&fit=crop&q=80"
            alt="Vision Background"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-africa-black/80 via-africa-green/60 to-transparent"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full mb-6">
                <Star className="w-12 h-12 text-africa-gold" />
              </div>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-africa-gold bg-clip-text text-transparent">
              From Vision to Reality
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-white/90">
              Meet the visionary behind Kennect-Afrinnect and join the movement to transform Kenya's future
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto mb-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-africa-gold mb-2">500+</div>
                <div className="text-white/80">Community Members</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-africa-gold mb-2">12</div>
                <div className="text-white/80">Active Projects</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-africa-gold mb-2">3</div>
                <div className="text-white/80">Years of Research</div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {user ? (
                <Button
                  onClick={() => setPostModalOpen(true)}
                  size="lg"
                  className="bg-africa-green hover:bg-africa-earth text-white px-8 py-4"
                >
                  <Plus className="mr-2 h-5 w-5" />
                  Create Post
                </Button>
              ) : (
                <Button
                  onClick={() => setAuthModalOpen(true)}
                  size="lg"
                  className="bg-africa-green hover:bg-africa-earth text-white px-8 py-4"
                >
                  <Edit className="mr-2 h-5 w-5" />
                  Login to Post
                </Button>
              )}
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white/10 px-8 py-4"
              >
                <MessageSquare className="mr-2 h-5 w-5" />
                Join Discussion
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-12">
            {/* Sidebar */}
            <div className="md:col-span-2">
              <div className="sticky top-24">
                <div className="rounded-xl overflow-hidden shadow-lg mb-6">
                  <img
                    src="https://images.unsplash.com/photo-1567446537708-ac4aa75c9c28?auto=format&fit=crop&q=80"
                    alt="Project Visionary"
                    className="w-full h-80 object-cover"
                  />
                </div>

                <div className="bg-africa-green text-white p-6 rounded-lg shadow-md mb-6">
                  <h3 className="text-xl font-bold mb-4">Support Our Vision</h3>
                  <p className="mb-4">
                    Your contribution helps us develop this platform further and implement resource-based economy principles in Kenya and Africa.
                  </p>
                  <Button className="w-full bg-white text-africa-green hover:bg-gray-100">
                    <Heart className="mr-2 h-4 w-4" />
                    Donate Now
                  </Button>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
                  <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
                  <ul className="space-y-3">
                    <li>
                      <Link to="/whitepaper" className="flex items-center text-africa-green hover:text-africa-earth">
                        <Download className="mr-2 h-4 w-4" />
                        Download Whitepaper
                      </Link>
                    </li>
                    <li>
                      <Link to="/timeline" className="flex items-center text-africa-green hover:text-africa-earth">
                        <ArrowRight className="mr-2 h-4 w-4" />
                        View Project Timeline
                      </Link>
                    </li>
                    <li>
                      <Link to="/register" className="flex items-center text-africa-green hover:text-africa-earth">
                        <ArrowRight className="mr-2 h-4 w-4" />
                        Join Our Community
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="md:col-span-3">
              <h2 className="text-3xl font-bold mb-6">My Journey & Vision</h2>

              <div className="prose prose-lg max-w-none">
                <p>
                  My name is Joseph Kabiu, the founder of Kennect-Afrinnect. My journey began with a simple question: Why do we continue to operate within economic systems that create artificial scarcity when our planet has abundant resources?
                </p>

                <p className="mt-4">
                  Inspired by visionaries like Jacque Fresco of The Venus Project, I've dedicated myself to adapting resource-based economy principles to the unique context of Kenya and the broader African continent. Our region faces unique challenges but also possesses immense resources and potential that, if properly harnessed, could transform lives.
                </p>

                <p className="mt-4">
                  Through Kennect-Afrinnect, I aim to bring together engineers, architects, farmers, educators, and visionaries to build sustainable communities that utilize our abundant resources efficiently and equitably, without the constraints of monetary systems that often perpetuate inequality.
                </p>

                <h3 className="text-2xl font-bold mt-8 mb-4">Our Approach</h3>

                <p>
                  We are adapting the circular city designs pioneered by The Venus Project to the African context. These designs emphasize:
                </p>

                <ul className="list-disc pl-6 my-4 space-y-2">
                  <li>Sustainable use of local resources and materials</li>
                  <li>Integration with natural ecosystems rather than destruction of them</li>
                  <li>Maximizing efficiency through thoughtful urban planning</li>
                  <li>Leveraging technology to automate repetitive tasks</li>
                  <li>Creating systems of abundance rather than artificial scarcity</li>
                </ul>

                <p className="mt-4">
                  Our immediate focus is on building a community of like-minded individuals who can contribute their skills, knowledge, and resources to this vision. We're starting small but thinking big, with plans to develop demonstration projects that showcase resource-based economy principles in action.
                </p>

                <h3 className="text-2xl font-bold mt-8 mb-4">Call to Action</h3>

                <p>
                  This journey is not one I can undertake alone. I'm looking for collaborators, contributors, and supporters who share this vision of a more equitable and sustainable future for Africa.
                </p>

                <p className="mt-4">
                  If you have skills in engineering, architecture, agriculture, education, or simply a passion for creating a better world, I invite you to join us. Every contribution, whether it's your time, expertise, or resources, brings us one step closer to realizing this vision.
                </p>

                <div className="bg-gray-50 p-6 rounded-lg my-8">
                  <h4 className="text-xl font-semibold mb-4">Support Our Development</h4>
                  <p className="mb-4">
                    We are currently seeking donations to help with website hosting, organizing community events, and developing educational materials. Your support makes a significant difference.
                  </p>
                  <div className="mb-4">
                    <Input
                      type="number"
                      placeholder="Enter amount in KES"
                      min="100"
                      className="mb-3"
                    />
                  </div>
                  <Button className="w-full bg-africa-green text-white hover:bg-africa-earth">
                    Make a Donation
                  </Button>
                </div>

                <p className="italic">
                  "We cannot solve our problems with the same thinking we used when we created them." - Albert Einstein
                </p>

                <p className="mt-8">
                  Thank you for taking the time to learn about our vision. Together, we can build a future where technology and resources serve all of humanity, not just a privileged few.
                </p>

                <div className="mt-8">
                  <Link to="/register">
                    <Button size="lg" className="bg-africa-green hover:bg-africa-earth text-white">
                      Join Our Movement <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultMode="login"
      />
    </div>
  );
};

export default Visionary;
