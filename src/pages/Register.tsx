
import React, { useState } from "react";
import { CheckCircle, ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";

const Register = () => {
  const { toast } = useToast();
  const [formState, setFormState] = useState({
    fullName: "",
    email: "",
    country: "",
    city: "",
    skills: [] as string[],
    resources: [] as string[],
    motivation: "",
    agreeToTerms: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  
  const skillOptions = [
    "Engineering", "Education", "Agriculture", "Software Development", 
    "Healthcare", "Construction", "Energy", "Logistics", 
    "Communication", "Art & Design", "Research", "Project Management"
  ];
  
  const resourceOptions = [
    "Land", "Materials", "3D Printing Equipment", "Funding", 
    "Volunteer Time", "Connections/Network", "Technical Equipment", 
    "Workshop/Maker Space"
  ];
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSkillToggle = (skill: string) => {
    setFormState((prev) => {
      if (prev.skills.includes(skill)) {
        return { ...prev, skills: prev.skills.filter((s) => s !== skill) };
      } else {
        return { ...prev, skills: [...prev.skills, skill] };
      }
    });
  };
  
  const handleResourceToggle = (resource: string) => {
    setFormState((prev) => {
      if (prev.resources.includes(resource)) {
        return { ...prev, resources: prev.resources.filter((r) => r !== resource) };
      } else {
        return { ...prev, resources: [...prev.resources, resource] };
      }
    });
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formState.agreeToTerms) {
      toast({
        title: "Please agree to the terms",
        description: "You must agree to our Shared Values and Code of Ethics to join.",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      console.log("Form submitted:", formState);
      
      toast({
        title: "Registration successful!",
        description: "Thank you for joining the Future Society Initiative. Check your email for confirmation.",
      });
    }, 1500);
  };
  
  if (isSuccess) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        
        <div className="flex-grow py-20 bg-rbe-silver">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-md text-center">
              <div className="mb-6 text-rbe-green">
                <CheckCircle size={64} className="mx-auto" />
              </div>
              <h1 className="text-3xl font-bold mb-4 text-rbe-navy">Welcome to the Movement!</h1>
              <p className="text-lg mb-6 text-gray-700">
                Thank you for registering with the Future Society Initiative. We've sent a confirmation 
                email to your inbox with details about next steps.
              </p>
              <p className="text-lg mb-8 text-gray-700">
                Together, we can build a world beyond scarcity, where technology serves humanity and 
                resources are managed for the benefit of all.
              </p>
              <Button className="bg-rbe-blue hover:bg-rbe-navy text-white" onClick={() => window.location.href = "/"}>
                Return to Homepage
              </Button>
            </div>
          </div>
        </div>
        
        <Footer />
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <Hero 
        title="Join the Movement" 
        subtitle="Register to contribute your skills and help build a Resource-Based Economy"
        showButtons={false}
      />
      
      <section className="py-20 bg-rbe-silver">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6 text-rbe-navy">Registration Form</h2>
            
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name*
                  </label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={formState.fullName}
                    onChange={handleChange}
                    required
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address*
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formState.email}
                    onChange={handleChange}
                    required
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                    Country*
                  </label>
                  <Input
                    id="country"
                    name="country"
                    value={formState.country}
                    onChange={handleChange}
                    required
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                    City*
                  </label>
                  <Input
                    id="city"
                    name="city"
                    value={formState.city}
                    onChange={handleChange}
                    required
                    className="w-full"
                  />
                </div>
              </div>
              
              <div className="mb-8">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Skills (select all that apply)
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                  {skillOptions.map((skill) => (
                    <div key={skill} className="flex items-center">
                      <Checkbox
                        id={`skill-${skill}`}
                        checked={formState.skills.includes(skill)}
                        onCheckedChange={() => handleSkillToggle(skill)}
                      />
                      <label htmlFor={`skill-${skill}`} className="ml-2 text-gray-700">
                        {skill}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mb-8">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Resources You Can Contribute (optional)
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                  {resourceOptions.map((resource) => (
                    <div key={resource} className="flex items-center">
                      <Checkbox
                        id={`resource-${resource}`}
                        checked={formState.resources.includes(resource)}
                        onCheckedChange={() => handleResourceToggle(resource)}
                      />
                      <label htmlFor={`resource-${resource}`} className="ml-2 text-gray-700">
                        {resource}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mb-8">
                <label htmlFor="motivation" className="block text-sm font-medium text-gray-700 mb-1">
                  Motivation Letter (optional)
                </label>
                <Textarea
                  id="motivation"
                  name="motivation"
                  value={formState.motivation}
                  onChange={handleChange}
                  placeholder="Tell us why you're interested in joining the Future Society Initiative..."
                  className="w-full h-32"
                />
              </div>
              
              <div className="mb-8">
                <div className="flex items-start">
                  <Checkbox
                    id="agreeToTerms"
                    checked={formState.agreeToTerms}
                    onCheckedChange={(checked) => 
                      setFormState((prev) => ({ ...prev, agreeToTerms: checked === true }))
                    }
                  />
                  <label htmlFor="agreeToTerms" className="ml-2 text-gray-700">
                    I agree to the <a href="#" className="text-rbe-blue hover:underline">Shared Values and Code of Ethics</a> of the Future Society Initiative
                  </label>
                </div>
              </div>
              
              <Button 
                type="submit" 
                className="bg-rbe-blue hover:bg-rbe-navy text-white w-full md:w-auto px-8"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Register"}
              </Button>
            </form>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Register;
