
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Ubuntu:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 20 14% 12%;

    --card: 0 0% 100%;
    --card-foreground: 20 14% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 20 14% 12%;

    --primary: 142 100% 26%;
    --primary-foreground: 40 33% 98%;

    --secondary: 45 80% 56%;
    --secondary-foreground: 20 14% 12%;

    --muted: 40 18% 96%;
    --muted-foreground: 20 6% 46%;

    --accent: 0 69% 58%;
    --accent-foreground: 40 33% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 40 33% 98%;

    --border: 20 10% 85%;
    --input: 20 10% 85%;
    --ring: 142 100% 26%;

    --radius: 0.5rem;

    --sidebar-background: 40 33% 98%;
    --sidebar-foreground: 20 6% 46%;
    --sidebar-primary: 142 100% 26%;
    --sidebar-primary-foreground: 40 33% 98%;
    --sidebar-accent: 20 10% 95%;
    --sidebar-accent-foreground: 20 14% 12%;
    --sidebar-border: 20 10% 85%;
    --sidebar-ring: 142 100% 26%;
  }

  .dark {
    --background: 20 14% 12%;
    --foreground: 40 33% 98%;

    --card: 20 14% 12%;
    --card-foreground: 40 33% 98%;

    --popover: 20 14% 12%;
    --popover-foreground: 40 33% 98%;

    --primary: 142 70% 35%;
    --primary-foreground: 40 33% 98%;

    --secondary: 45 60% 50%;
    --secondary-foreground: 20 14% 12%;

    --muted: 20 8% 20%;
    --muted-foreground: 20 6% 80%;

    --accent: 0 60% 50%;
    --accent-foreground: 40 33% 98%;

    --destructive: 0 62% 40%;
    --destructive-foreground: 40 33% 98%;

    --border: 20 8% 25%;
    --input: 20 8% 25%;
    --ring: 142 70% 35%;

    --sidebar-background: 20 14% 12%;
    --sidebar-foreground: 20 6% 80%;
    --sidebar-primary: 142 70% 35%;
    --sidebar-primary-foreground: 40 33% 98%;
    --sidebar-accent: 20 8% 18%;
    --sidebar-accent-foreground: 20 6% 80%;
    --sidebar-border: 20 8% 25%;
    --sidebar-ring: 142 70% 35%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-ubuntu;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-montserrat font-medium;
  }

  .african-gradient {
    @apply bg-gradient-to-r from-africa-green via-africa-earth to-africa-sunset bg-[length:200%_auto] animate-gradient;
  }
}

.hero-overlay {
  background: linear-gradient(to bottom, rgba(0, 135, 81, 0.85), rgba(210, 180, 140, 0.7));
}

.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.circular-city {
  position: relative;
}

.circular-city::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(0,135,81,0) 0%, rgba(0,135,81,0.2) 70%, rgba(0,135,81,0.4) 100%);
  pointer-events: none;
}

/* 3D canvas styles */
.canvas-container {
  position: relative;
  height: 60vh;
  min-height: 400px;
  width: 100%;
  overflow: hidden;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  padding: 1rem;
  z-index: 10;
}

/* Social media embeds */
.social-embed {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.social-embed:hover {
  transform: translateY(-4px);
}

/* Timeline styles */
.timeline-container {
  position: relative;
  padding-left: 2rem;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #008751, #DAA520);
}

.timeline-item {
  position: relative;
  padding-bottom: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -2rem;
  top: 0.5rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: #008751;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #008751;
}

/* Button interactions */
.interaction-button {
  @apply transition-all duration-200 ease-in-out;
}

.interaction-button:hover {
  @apply transform scale-110;
}

.interaction-button:active {
  @apply transform scale-95;
}

/* Line clamp utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Blog post content styling */
.prose {
  max-width: none;
}

.prose h2 {
  @apply text-2xl font-bold text-africa-black mt-8 mb-4;
}

.prose h3 {
  @apply text-xl font-semibold text-africa-black mt-6 mb-3;
}

.prose p {
  @apply text-gray-700 leading-relaxed mb-4;
}

.prose ul {
  @apply list-disc list-inside mb-4 space-y-2;
}

.prose li {
  @apply text-gray-700;
}
