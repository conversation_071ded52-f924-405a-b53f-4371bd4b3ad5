import type { VercelRequest, VercelResponse } from '@vercel/node'

// Mock blog posts data - in production, this would come from a database
const blogPosts = [
  {
    id: "1",
    title: "Building Sustainable Communities in Kenya",
    slug: "building-sustainable-communities-kenya",
    excerpt: "Exploring how resource-based economics can transform local communities across Kenya, creating sustainable and equitable living conditions for all.",
    content: "Full article content here...",
    author: { name: "Dr. <PERSON><PERSON>", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
    publishedAt: "2024-01-15",
    category: { id: "1", name: "Community Development", slug: "community", color: "#008751" },
    readTime: 8,
    featuredImage: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?auto=format&fit=crop&q=80",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    videoPlatform: "youtube" as const,
    tags: ["sustainability", "community", "development"]
  },
  {
    id: "2",
    title: "Technology for Resource Management",
    slug: "technology-resource-management",
    excerpt: "How modern technology can help us better manage and distribute Kenya's natural resources for maximum benefit to all citizens.",
    content: "Full article content here...",
    author: { name: "Prof. James Mwangi", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" },
    publishedAt: "2024-01-10",
    category: { id: "2", name: "Technology", slug: "technology", color: "#4682B4" },
    readTime: 6,
    featuredImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&q=80",
    tags: ["technology", "resources", "management"]
  }
]

export default function handler(req: VercelRequest, res: VercelResponse) {
  const { method } = req

  switch (method) {
    case 'GET':
      const { category, search } = req.query
      
      let filteredPosts = [...blogPosts]
      
      // Filter by category
      if (category && category !== 'all') {
        filteredPosts = filteredPosts.filter(post => post.category.slug === category)
      }
      
      // Filter by search
      if (search) {
        const searchTerm = search.toString().toLowerCase()
        filteredPosts = filteredPosts.filter(post => 
          post.title.toLowerCase().includes(searchTerm) ||
          post.excerpt.toLowerCase().includes(searchTerm) ||
          post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        )
      }
      
      res.status(200).json({
        posts: filteredPosts,
        total: filteredPosts.length
      })
      break
      
    case 'POST':
      // In production, this would create a new blog post
      res.status(201).json({
        message: 'Blog post created successfully',
        post: req.body
      })
      break
      
    default:
      res.setHeader('Allow', ['GET', 'POST'])
      res.status(405).end(`Method ${method} Not Allowed`)
  }
}
